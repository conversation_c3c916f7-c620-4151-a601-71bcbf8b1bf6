{"name": "allincloth", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@pothos/core": "^4.7.0", "@pothos/plugin-errors": "^4.4.1", "@pothos/plugin-prisma": "^4.9.1", "@pothos/plugin-relay": "^4.4.3", "@pothos/plugin-validation": "^3.10.2", "@prisma/client": "^6.10.1", "graphql": "^16.11.0", "graphql-yoga": "^5.13.5", "next": "15.3.3", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "typescript": "^5"}}