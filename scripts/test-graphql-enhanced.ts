// Test script for GraphQL API with enhanced pagination
import fetch from 'node-fetch';

const GRAPHQL_ENDPOINT = 'http://localhost:3000/api/graphql';

async function testGraphQL() {
  console.log('🧪 Testing GraphQL API with Enhanced Pagination and Filtering...\n');
  
  try {
    // Test 1: Basic health check via GraphQL introspection
    console.log('1. Testing GraphQL schema introspection...');
    const introspectionQuery = `
      query {
        __schema {
          types {
            name
          }
        }
      }
    `;
    
    const introspectionResponse = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: introspectionQuery }),
    });
    
    const introspectionResult = await introspectionResponse.json();
    if (introspectionResult.data) {
      console.log('✅ Schema introspection successful');
      console.log(`Found ${introspectionResult.data.__schema.types.length} types`);
    } else {
      console.log('❌ Schema introspection failed:', introspectionResult.errors);
      return;
    }
    
    // Test 2: Test categories (should work)
    console.log('\n2. Testing categories query...');
    const categoriesQuery = `
      query {
        categories {
          edges {
            node {
              id
              name
            }
          }
          totalCount
        }
      }
    `;
    
    const categoriesResponse = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: categoriesQuery }),
    });
    
    const categoriesResult = await categoriesResponse.json();
    if (categoriesResult.data) {
      console.log('✅ Categories query successful');
      console.log(`Found ${categoriesResult.data.categories.totalCount} categories`);
    } else {
      console.log('❌ Categories query failed:', categoriesResult.errors);
    }
    
    // Test 3: Test traditional pagination query for products
    console.log('\n3. Testing products with traditional pagination...');
    const productsQuery = `
      query {
        products(
          pagination: {
            page: 1
            pageSize: 2
          }
          filter: {
            nameContains: "shirt"
          }
          sort: {
            field: name
            direction: ASC
          }
        ) {
          products {
            id
            name
            description
            category {
              name
            }
          }
          pageInfo {
            page
            pageSize
            totalPages
            totalCount
            hasNextPage
            hasPreviousPage
          }
        }
      }
    `;
    
    const productsResponse = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: productsQuery }),
    });
    
    const productsResult = await productsResponse.json();
    if (productsResult.data && productsResult.data.products) {
      console.log('✅ Products with pagination successful');
      console.log(`Found ${productsResult.data.products.products.length} products on page ${productsResult.data.products.pageInfo.page}`);
      console.log(`Total: ${productsResult.data.products.pageInfo.totalCount} products, ${productsResult.data.products.pageInfo.totalPages} pages`);
    } else {
      console.log('❌ Products query failed:', productsResult.errors);
    }
    
    // Test 4: Test comprehensive product filtering
    console.log('\n4. Testing comprehensive product filtering...');
    const filteredProductsQuery = `
      query {
        products(
          pagination: {
            page: 1
            pageSize: 5
          }
          filter: {
            inStock: true
            hasImages: true
            categoryNameContains: "clothing"
          }
        ) {
          products {
            id
            name
            category {
              name
            }
          }
          pageInfo {
            totalCount
            hasNextPage
          }
        }
      }
    `;
    
    const filteredResponse = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: filteredProductsQuery }),
    });
    
    const filteredResult = await filteredResponse.json();
    if (filteredResult.data && filteredResult.data.products) {
      console.log('✅ Comprehensive filtering successful');
      console.log(`Found ${filteredResult.data.products.pageInfo.totalCount} in-stock products with images`);
    } else {
      console.log('❌ Comprehensive filtering failed:', filteredResult.errors);
    }
    
    // Test 5: Test offset-based pagination
    console.log('\n5. Testing offset-based pagination...');
    const offsetQuery = `
      query {
        products(
          pagination: {
            offset: 2
            pageSize: 3
          }
        ) {
          products {
            id
            name
          }
          pageInfo {
            page
            totalCount
          }
        }
      }
    `;
    
    const offsetResponse = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: offsetQuery }),
    });
    
    const offsetResult = await offsetResponse.json();
    if (offsetResult.data && offsetResult.data.products) {
      console.log('✅ Offset-based pagination successful');
      console.log(`Retrieved products starting from offset 2`);
    } else {
      console.log('❌ Offset-based pagination failed:', offsetResult.errors);
    }
    
    console.log('\n🎉 GraphQL API testing completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

testGraphQL();
