import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Clear existing data
  await prisma.orderItem.deleteMany({})
  await prisma.order.deleteMany({})
  await prisma.variantDiscount.deleteMany({})
  await prisma.discount.deleteMany({})
  await prisma.productVariant.deleteMany({})
  await prisma.productImage.deleteMany({})
  await prisma.product.deleteMany({})
  await prisma.size.deleteMany({})
  await prisma.color.deleteMany({})
  await prisma.category.deleteMany({})
  await prisma.customer.deleteMany({})

  console.log('🧹 Cleared existing data')

  // Create categories
  const categories = await Promise.all([
    prisma.category.create({
      data: { name: 'T-Shirts' }
    }),
    prisma.category.create({
      data: { name: '<PERSON><PERSON>' }
    }),
    prisma.category.create({
      data: { name: 'Hood<PERSON>' }
    }),
    prisma.category.create({
      data: { name: 'Accessories' }
    })
  ])

  console.log('📂 Created categories')

  // Create sizes
  const sizes = await Promise.all([
    prisma.size.create({ data: { sizeLabel: 'XS' } }),
    prisma.size.create({ data: { sizeLabel: 'S' } }),
    prisma.size.create({ data: { sizeLabel: 'M' } }),
    prisma.size.create({ data: { sizeLabel: 'L' } }),
    prisma.size.create({ data: { sizeLabel: 'XL' } }),
    prisma.size.create({ data: { sizeLabel: 'XXL' } })
  ])

  console.log('📏 Created sizes')

  // Create colors
  const colors = await Promise.all([
    prisma.color.create({ data: { colorName: 'Black', hexCode: '#000000' } }),
    prisma.color.create({ data: { colorName: 'White', hexCode: '#FFFFFF' } }),
    prisma.color.create({ data: { colorName: 'Navy Blue', hexCode: '#1e3a8a' } }),
    prisma.color.create({ data: { colorName: 'Red', hexCode: '#dc2626' } }),
    prisma.color.create({ data: { colorName: 'Gray', hexCode: '#6b7280' } })
  ])

  console.log('🎨 Created colors')

  // Create products
  const products = await Promise.all([
    prisma.product.create({
      data: {
        name: 'Classic Cotton T-Shirt',
        description: 'A comfortable and versatile cotton t-shirt perfect for everyday wear.',
        categoryId: categories[0].id
      }
    }),
    prisma.product.create({
      data: {
        name: 'Premium Denim Jeans',
        description: 'High-quality denim jeans with a perfect fit and modern styling.',
        categoryId: categories[1].id
      }
    }),
    prisma.product.create({
      data: {
        name: 'Cozy Pullover Hoodie',
        description: 'Warm and comfortable hoodie made from soft cotton blend.',
        categoryId: categories[2].id
      }
    }),
    prisma.product.create({
      data: {
        name: 'Vintage Graphic Tee',
        description: 'Trendy graphic t-shirt with vintage-inspired design.',
        categoryId: categories[0].id
      }
    })
  ])

  console.log('👕 Created products')

  // Create product images
  const productImages = []
  for (let i = 0; i < products.length; i++) {
    const product = products[i]
    await prisma.productImage.createMany({
      data: [
        {
          productId: product.id,
          imageUrl: `https://example.com/images/${product.name.toLowerCase().replace(/\s+/g, '-')}-1.jpg`,
          isPrimary: true
        },
        {
          productId: product.id,
          imageUrl: `https://example.com/images/${product.name.toLowerCase().replace(/\s+/g, '-')}-2.jpg`,
          isPrimary: false
        }
      ]
    })
  }

  console.log('📸 Created product images')

  // Create product variants
  const variants = []
  for (const product of products) {
    for (let sizeIndex = 1; sizeIndex < 4; sizeIndex++) { // S, M, L
      for (let colorIndex = 0; colorIndex < 3; colorIndex++) { // First 3 colors
        const size = sizes[sizeIndex]
        const color = colors[colorIndex]
        const basePrice = product.name.includes('Premium') ? 89.99 : 
                         product.name.includes('Hoodie') ? 59.99 : 29.99
        
        const variant = await prisma.productVariant.create({
          data: {
            productId: product.id,
            sizeId: size.id,
            colorId: color.id,
            sku: `${product.name.substring(0, 3).toUpperCase()}-${size.sizeLabel}-${color.colorName.substring(0, 3).toUpperCase()}`,
            price: basePrice,
            quantity: Math.floor(Math.random() * 100) + 10 // 10-109 items
          }
        })
        variants.push(variant)
      }
    }
  }

  console.log('🔀 Created product variants')

  // Create customers
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+1234567890'
      }
    }),
    prisma.customer.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        phone: '+1987654321'
      }
    }),
    prisma.customer.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Bob',
        lastName: 'Wilson'
      }
    })
  ])

  console.log('👥 Created customers')

  // Create discounts
  const discount = await prisma.discount.create({
    data: {
      name: 'Summer Sale 2025',
      description: '20% off on selected items',
      discountPercent: 20.0,
      startDate: new Date('2025-06-01'),
      endDate: new Date('2025-08-31')
    }
  })

  // Apply discount to some variants
  const discountVariants = variants.slice(0, 5) // First 5 variants
  await prisma.variantDiscount.createMany({
    data: discountVariants.map(variant => ({
      productVariantId: variant.id,
      discountId: discount.id
    }))
  })

  console.log('💰 Created discounts')

  // Create sample orders
  const orders = []
  for (let i = 0; i < customers.length; i++) {
    const customer = customers[i]
    const orderVariants = variants.slice(i * 2, (i * 2) + 2) // 2 variants per order
    
    const totalAmount = orderVariants.reduce((sum, variant) => sum + (variant.price * 1), 0)
    
    const order = await prisma.order.create({
      data: {
        customerId: customer.id,
        totalAmount,
        status: i === 0 ? 'delivered' : i === 1 ? 'shipped' : 'pending',
        items: {
          create: orderVariants.map(variant => ({
            productVariantId: variant.id,
            quantity: 1,
            priceAtPurchase: variant.price
          }))
        }
      }
    })
    orders.push(order)
  }

  console.log('🛒 Created orders')

  console.log('\n🎉 Database seeding completed successfully!')
  console.log('\n📊 Summary:')
  console.log(`   Categories: ${categories.length}`)
  console.log(`   Sizes: ${sizes.length}`)
  console.log(`   Colors: ${colors.length}`)
  console.log(`   Products: ${products.length}`)
  console.log(`   Product Variants: ${variants.length}`)
  console.log(`   Customers: ${customers.length}`)
  console.log(`   Orders: ${orders.length}`)
  console.log(`   Discounts: 1`)
  
  console.log('\n🚀 You can now:')
  console.log('   1. Start the development server: npm run dev')
  console.log('   2. Open GraphQL Playground: http://localhost:3000/api/graphql')
  console.log('   3. Try the example queries from GraphQL_API_Examples.md')
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
