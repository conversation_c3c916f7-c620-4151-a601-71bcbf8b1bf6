import { graphql, buildClientSchema, getIntrospectionQuery } from 'graphql'

const GRAPHQL_ENDPOINT = 'http://localhost:3001/api/graphql'

async function testGraphQL() {
  console.log('🚀 Testing GraphQL API...\n')

  // Test 1: Simple Categories Query
  console.log('📝 Testing categories query...')
  const categoriesQuery = `
    query GetCategories {
      categories {
        edges {
          node {
            id
            name
            description
          }
        }
      }
    }
  `

  try {
    const categoriesResponse = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: categoriesQuery,
      }),
    })

    const categoriesResult = await categoriesResponse.json()
    console.log('✅ Categories:', JSON.stringify(categoriesResult, null, 2))
  } catch (error) {
    console.error('❌ Categories query failed:', error)
  }

  // Test 2: Products with Variants
  console.log('\n📝 Testing products with variants query...')
  const productsQuery = `
    query GetProducts {
      products(first: 2) {
        edges {
          node {
            id
            name
            description
            category {
              name
            }
            variants(first: 3) {
              edges {
                node {
                  id
                  sku
                  price
                  currentPrice
                  quantity
                  size {
                    name
                  }
                  color {
                    name
                    hexCode
                  }
                }
              }
            }
            priceRange {
              min
              max
            }
          }
        }
      }
    }
  `

  try {
    const productsResponse = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: productsQuery,
      }),
    })

    const productsResult = await productsResponse.json()
    console.log('✅ Products:', JSON.stringify(productsResult, null, 2))
  } catch (error) {
    console.error('❌ Products query failed:', error)
  }

  // Test 3: Single Product Query
  console.log('\n📝 Testing single product query...')
  const singleProductQuery = `
    query GetProduct($id: ID!) {
      product(id: $id) {
        id
        name
        description
        category {
          name
        }
        variants(first: 5) {
          edges {
            node {
              id
              sku
              price
              currentPrice
              quantity
              size {
                name
              }
              color {
                name
                hexCode
              }
            }
          }
        }
        images(first: 3) {
          edges {
            node {
              id
              url
              altText
              isPrimary
            }
          }
        }
      }
    }
  `

  try {
    // First get a product ID from the previous query
    const allProductsResponse = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `query { products(first: 1) { edges { node { id } } } }`
      }),
    })

    const allProductsResult = await allProductsResponse.json()
    const productId = allProductsResult.data?.products?.edges?.[0]?.node?.id

    if (productId) {
      const singleProductResponse = await fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: singleProductQuery,
          variables: { id: productId }
        }),
      })

      const singleProductResult = await singleProductResponse.json()
      console.log('✅ Single Product:', JSON.stringify(singleProductResult, null, 2))
    } else {
      console.log('⚠️ No product ID found for single product test')
    }
  } catch (error) {
    console.error('❌ Single product query failed:', error)
  }

  console.log('\n🎉 GraphQL API testing completed!')
}

testGraphQL().catch(console.error)
