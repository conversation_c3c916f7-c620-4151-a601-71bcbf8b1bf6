# AllInCloth GraphQL API Examples

This document provides example queries and mutations for the AllInCloth GraphQL API.

## Authentication
Currently, the API doesn't require authentication, but it's structured to easily add authentication middleware.

## Base URL
- Development: `http://localhost:3000/api/graphql`
- GraphiQL: `http://localhost:3000/api/graphql` (in development mode)

## Example Queries

### 1. Categories

#### Get all categories
```graphql
query GetCategories {
  categories {
    edges {
      node {
        id
        name
        products {
          totalCount
        }
      }
    }
    totalCount
  }
}
```

#### Get a specific category
```graphql
query GetCategory($id: String!) {
  category(id: $id) {
    id
    name
    products {
      edges {
        node {
          id
          name
          description
          primaryImage
        }
      }
    }
  }
}
```

### 2. Products

#### Get products with filtering and sorting
```graphql
query GetProducts(
  $filter: ProductFilterInput
  $sort: ProductSortInput
  $first: Int
) {
  products(filter: $filter, sort: $sort, first: $first) {
    edges {
      node {
        id
        name
        description
        primaryImage
        priceRange {
          min
          max
        }
        category {
          name
        }
        variants {
          totalCount
        }
      }
    }
    totalCount
    pageInfo {
      hasNextPage
      hasPreviousPage
      startCursor
      endCursor
    }
  }
}
```

Variables:
```json
{
  "filter": {
    "categoryId": "category_id_here",
    "name": "shirt",
    "priceMin": 10.0,
    "priceMax": 100.0
  },
  "sort": {
    "field": "NAME",
    "direction": "ASC"
  },
  "first": 10
}
```

#### Get product details
```graphql
query GetProductDetail($id: String!) {
  product(id: $id) {
    id
    name
    description
    category {
      id
      name
    }
    images {
      edges {
        node {
          id
          imageUrl
          isPrimary
        }
      }
    }
    variants {
      edges {
        node {
          id
          sku
          price
          currentPrice
          quantity
          isAvailable
          size {
            sizeLabel
          }
          color {
            colorName
            hexCode
          }
        }
      }
    }
  }
}
```

### 3. Product Variants

#### Get variants with filtering
```graphql
query GetProductVariants($filter: ProductVariantFilterInput) {
  productVariants(filter: $filter) {
    edges {
      node {
        id
        sku
        price
        currentPrice
        quantity
        isAvailable
        product {
          name
        }
        size {
          sizeLabel
        }
        color {
          colorName
          hexCode
        }
      }
    }
  }
}
```

### 4. Customers

#### Get customers
```graphql
query GetCustomers($filter: CustomerFilterInput) {
  customers(filter: $filter, first: 20) {
    edges {
      node {
        id
        email
        fullName
        phone
        createdAt
        orderStats {
          totalOrders
          totalSpent
          averageOrderValue
        }
      }
    }
  }
}
```

### 5. Orders

#### Get orders with filtering
```graphql
query GetOrders($filter: OrderFilterInput, $sort: OrderSortInput) {
  orders(filter: $filter, sort: $sort) {
    edges {
      node {
        id
        orderDate
        totalAmount
        status
        customer {
          fullName
          email
        }
        orderSummary {
          totalItems
          itemCount
        }
        items {
          edges {
            node {
              id
              quantity
              priceAtPurchase
              lineTotal
              productVariant {
                sku
                product {
                  name
                }
                size {
                  sizeLabel
                }
                color {
                  colorName
                }
              }
            }
          }
        }
      }
    }
  }
}
```

## Example Mutations

### 1. Create Category
```graphql
mutation CreateCategory($input: CreateCategoryInput!) {
  createCategory(input: $input) {
    id
    name
  }
}
```

Variables:
```json
{
  "input": {
    "name": "T-Shirts"
  }
}
```

### 2. Create Product
```graphql
mutation CreateProduct($input: CreateProductInput!) {
  createProduct(input: $input) {
    id
    name
    description
    category {
      name
    }
  }
}
```

Variables:
```json
{
  "input": {
    "name": "Classic Cotton T-Shirt",
    "description": "A comfortable and stylish cotton t-shirt perfect for everyday wear.",
    "categoryId": "category_id_here"
  }
}
```

### 3. Create Size and Color
```graphql
mutation CreateSize($input: CreateSizeInput!) {
  createSize(input: $input) {
    id
    sizeLabel
  }
}

mutation CreateColor($input: CreateColorInput!) {
  createColor(input: $input) {
    id
    colorName
    hexCode
  }
}
```

Variables:
```json
{
  "input": {
    "sizeLabel": "M"
  }
}
```

```json
{
  "input": {
    "colorName": "Navy Blue",
    "hexCode": "#1e3a8a"
  }
}
```

### 4. Create Product Variant
```graphql
mutation CreateProductVariant($input: CreateProductVariantInput!) {
  createProductVariant(input: $input) {
    id
    sku
    price
    quantity
    product {
      name
    }
    size {
      sizeLabel
    }
    color {
      colorName
    }
  }
}
```

Variables:
```json
{
  "input": {
    "productId": "product_id_here",
    "sizeId": "size_id_here",
    "colorId": "color_id_here",
    "sku": "SHIRT-001-M-NAVY",
    "price": 29.99,
    "quantity": 100
  }
}
```

### 5. Create Customer
```graphql
mutation CreateCustomer($input: CreateCustomerInput!) {
  createCustomer(input: $input) {
    id
    email
    fullName
    phone
  }
}
```

Variables:
```json
{
  "input": {
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567890"
  }
}
```

### 6. Create Order
```graphql
mutation CreateOrder($input: CreateOrderInput!) {
  createOrder(input: $input) {
    id
    orderDate
    totalAmount
    status
    customer {
      fullName
    }
    items {
      edges {
        node {
          quantity
          priceAtPurchase
          productVariant {
            sku
            product {
              name
            }
          }
        }
      }
    }
  }
}
```

Variables:
```json
{
  "input": {
    "customerId": "customer_id_here",
    "items": [
      {
        "productVariantId": "variant_id_here",
        "quantity": 2
      },
      {
        "productVariantId": "variant_id_2_here",
        "quantity": 1
      }
    ]
  }
}
```

### 7. Update Order Status
```graphql
mutation UpdateOrderStatus($input: UpdateOrderStatusInput!) {
  updateOrderStatus(input: $input) {
    id
    status
    orderDate
    totalAmount
  }
}
```

Variables:
```json
{
  "input": {
    "id": "order_id_here",
    "status": "SHIPPED"
  }
}
```

### 8. Create and Apply Discount
```graphql
mutation CreateDiscount($input: CreateDiscountInput!) {
  createDiscount(input: $input) {
    id
    name
    discountPercent
    startDate
    endDate
    isActive
  }
}

mutation ApplyDiscountToVariants($input: ApplyDiscountInput!) {
  applyDiscountToVariants(input: $input)
}
```

Variables:
```json
{
  "input": {
    "name": "Summer Sale",
    "description": "20% off summer collection",
    "discountPercent": 20.0,
    "startDate": "2025-06-01T00:00:00Z",
    "endDate": "2025-08-31T23:59:59Z"
  }
}
```

```json
{
  "input": {
    "discountId": "discount_id_here",
    "productVariantIds": ["variant1_id", "variant2_id"]
  }
}
```

## Error Handling

The API returns structured errors with codes:

```graphql
{
  "errors": [
    {
      "message": "Product not found",
      "extensions": {
        "code": "NOT_FOUND",
        "statusCode": 404
      }
    }
  ]
}
```

Common error codes:
- `VALIDATION_ERROR`: Invalid input data
- `NOT_FOUND`: Resource not found
- `CONFLICT`: Duplicate resource
- `INSUFFICIENT_STOCK`: Not enough inventory
- `DATABASE_ERROR`: Database operation failed

## Health Check

```
GET /api/health
```

Returns:
```json
{
  "status": "healthy",
  "timestamp": "2025-06-18T10:00:00Z",
  "database": "connected",
  "environment": "development"
}
```
