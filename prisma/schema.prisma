generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model Category {
  id       String    @id @default(auto()) @map("_id") @db.ObjectId
  name     String    @unique
  products Product[]
}

model Product {
  id          String           @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String
  category    Category         @relation(fields: [categoryId], references: [id])
  categoryId  String           @db.ObjectId
  variants    ProductVariant[]
  images      ProductImage[]
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
}

model ProductImage {
  id        String  @id @default(auto()) @map("_id") @db.ObjectId
  product   Product @relation(fields: [productId], references: [id])
  productId String  @db.ObjectId
  imageUrl  String
  isPrimary Boolean @default(false)
}

model Size {
  id        String           @id @default(auto()) @map("_id") @db.ObjectId
  sizeLabel String           @unique
  variants  ProductVariant[]
}

model Color {
  id        String           @id @default(auto()) @map("_id") @db.ObjectId
  colorName String           @unique
  hexCode   String?
  variants  ProductVariant[]
}

model ProductVariant {
  id        String @id @default(auto()) @map("_id") @db.ObjectId
  product   Product @relation(fields: [productId], references: [id])
  productId String  @db.ObjectId

  size   Size   @relation(fields: [sizeId], references: [id])
  sizeId String @db.ObjectId

  color   Color  @relation(fields: [colorId], references: [id])
  colorId String @db.ObjectId

  sku       String @unique
  price     Float
  quantity  Int
  
  discounts VariantDiscount[]
  orderItems OrderItem[]

  @@unique([productId, sizeId, colorId])
}

model Discount {
  id              String            @id @default(auto()) @map("_id") @db.ObjectId
  name            String
  description     String?
  discountPercent Float?
  discountAmount  Float?
  startDate       DateTime
  endDate         DateTime
  variants        VariantDiscount[]
}

model VariantDiscount {
  id               String         @id @default(auto()) @map("_id") @db.ObjectId
  productVariant   ProductVariant @relation(fields: [productVariantId], references: [id])
  productVariantId String         @db.ObjectId

  discount   Discount @relation(fields: [discountId], references: [id])
  discountId String   @db.ObjectId

  @@unique([productVariantId, discountId])
}

model Customer {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  email     String   @unique
  firstName String
  lastName  String
  phone     String?
  createdAt DateTime @default(now())
  orders    Order[]
}

model Order {
  id          String      @id @default(auto()) @map("_id") @db.ObjectId
  customer    Customer    @relation(fields: [customerId], references: [id])
  customerId  String      @db.ObjectId
  orderDate   DateTime    @default(now())
  totalAmount Float
  status      OrderStatus @default(pending)
  items       OrderItem[]
}

model OrderItem {
  id               String         @id @default(auto()) @map("_id") @db.ObjectId
  order            Order          @relation(fields: [orderId], references: [id])
  orderId          String         @db.ObjectId

  productVariant   ProductVariant @relation(fields: [productVariantId], references: [id])
  productVariantId String         @db.ObjectId

  quantity        Int
  priceAtPurchase Float
}

enum OrderStatus {
  pending
  shipped
  delivered
  cancelled
}
