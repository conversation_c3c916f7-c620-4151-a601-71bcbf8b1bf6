generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model Category {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String    @unique
  description String?
  slug        String    @unique
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  parentId    String?   @db.ObjectId
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  children    Category[] @relation("CategoryHierarchy")
  products    Product[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("categories")
}

model Product {
  id          String           @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String
  slug        String           @unique
  sku         String?          @unique
  brand       String?
  tags        String[]
  isActive    Boolean          @default(true)
  isFeatured  Boolean          @default(false)
  weight      Float?
  dimensions  Json?            // {length, width, height}
  material    String?
  careInstructions String?
  category    Category         @relation(fields: [categoryId], references: [id])
  categoryId  String           @db.ObjectId
  variants    ProductVariant[]
  images      ProductImage[]
  reviews     ProductReview[]
  wishlistItems WishlistItem[]
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  @@map("products")
}

model ProductImage {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  product   Product  @relation(fields: [productId], references: [id])
  productId String   @db.ObjectId
  imageUrl  String
  altText   String?
  isPrimary Boolean  @default(false)
  sortOrder Int      @default(0)
  createdAt DateTime @default(now())

  @@map("product_images")
}

model ProductReview {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  product   Product  @relation(fields: [productId], references: [id])
  productId String   @db.ObjectId
  customer  Customer @relation(fields: [customerId], references: [id])
  customerId String  @db.ObjectId
  rating    Int      // 1-5 stars
  title     String?
  comment   String?
  isVerified Boolean @default(false)
  isApproved Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("product_reviews")
}

model Size {
  id         String           @id @default(auto()) @map("_id") @db.ObjectId
  sizeLabel  String           @unique
  category   String?          // e.g., "clothing", "shoes"
  sortOrder  Int              @default(0)
  isActive   Boolean          @default(true)
  variants   ProductVariant[]
  createdAt  DateTime         @default(now())

  @@map("sizes")
}

model Color {
  id         String           @id @default(auto()) @map("_id") @db.ObjectId
  colorName  String           @unique
  hexCode    String?
  isActive   Boolean          @default(true)
  sortOrder  Int              @default(0)
  variants   ProductVariant[]
  createdAt  DateTime         @default(now())

  @@map("colors")
}

model ProductVariant {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  product    Product  @relation(fields: [productId], references: [id])
  productId  String   @db.ObjectId

  size       Size     @relation(fields: [sizeId], references: [id])
  sizeId     String   @db.ObjectId

  color      Color    @relation(fields: [colorId], references: [id])
  colorId    String   @db.ObjectId

  sku        String   @unique
  price      Float
  compareAtPrice Float? // Original price for discount display
  costPrice  Float?     // Cost for profit calculation
  quantity   Int
  lowStockThreshold Int @default(5)
  isActive   Boolean  @default(true)
  weight     Float?
  barcode    String?  @unique

  discounts  VariantDiscount[]
  orderItems OrderItem[]
  cartItems  CartItem[]
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([productId, sizeId, colorId])
  @@map("product_variants")
}

model Discount {
  id              String            @id @default(auto()) @map("_id") @db.ObjectId
  name            String
  description     String?
  code            String?           @unique // Coupon code
  discountType    DiscountType      @default(PERCENTAGE)
  discountPercent Float?
  discountAmount  Float?
  minimumAmount   Float? // Minimum order amount
  maximumDiscount Float? // Maximum discount amount
  usageLimit      Int?              // Total usage limit
  usageCount      Int               @default(0)
  isActive        Boolean           @default(true)
  startDate       DateTime
  endDate         DateTime
  variants        VariantDiscount[]
  orders          Order[]
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  @@map("discounts")
}

model VariantDiscount {
  id                String         @id @default(auto()) @map("_id") @db.ObjectId
  productVariant    ProductVariant @relation(fields: [productVariantId], references: [id])
  productVariantId  String         @db.ObjectId

  discount          Discount       @relation(fields: [discountId], references: [id])
  discountId        String         @db.ObjectId

  @@unique([productVariantId, discountId])
  @@map("variant_discounts")
}

model Customer {
  id         String    @id @default(auto()) @map("_id") @db.ObjectId
  email      String    @unique
  firstName  String
  lastName   String
  phone      String?
  dateOfBirth DateTime?
  gender     Gender?
  isActive   Boolean   @default(true)
  isVerified Boolean   @default(false)
  lastLoginAt DateTime?
  addresses  Address[]
  orders     Order[]
  reviews    ProductReview[]
  cart       Cart?
  wishlist   Wishlist?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@map("customers")
}

model Address {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  customer    Customer  @relation(fields: [customerId], references: [id])
  customerId  String    @db.ObjectId
  type        AddressType @default(SHIPPING)
  firstName   String
  lastName    String
  company     String?
  address1    String
  address2    String?
  city        String
  state       String
  postalCode  String
  country     String    @default("US")
  phone       String?
  isDefault   Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("addresses")
}

model Order {
  id              String       @id @default(auto()) @map("_id") @db.ObjectId
  orderNumber     String       @unique
  customer        Customer     @relation(fields: [customerId], references: [id])
  customerId      String       @db.ObjectId
  orderDate       DateTime     @default(now())
  subtotalAmount  Float
  taxAmount       Float @default(0)
  shippingAmount  Float @default(0)
  discountAmount  Float @default(0)
  totalAmount     Float
  status          OrderStatus  @default(PENDING)
  paymentStatus   PaymentStatus @default(PENDING)
  shippingMethod  String?
  trackingNumber  String?
  notes           String?

  // Address information (snapshot at time of order)
  shippingAddress Json
  billingAddress  Json?

  discount        Discount?    @relation(fields: [discountId], references: [id])
  discountId      String?      @db.ObjectId

  items           OrderItem[]
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  @@map("orders")
}

model OrderItem {
  id                String         @id @default(auto()) @map("_id") @db.ObjectId
  order             Order          @relation(fields: [orderId], references: [id])
  orderId           String         @db.ObjectId

  productVariant    ProductVariant @relation(fields: [productVariantId], references: [id])
  productVariantId  String         @db.ObjectId

  quantity          Int
  priceAtPurchase   Float
  discountAmount    Float @default(0)

  // Product snapshot at time of purchase
  productSnapshot   Json

  @@map("order_items")
}

model Cart {
  id         String     @id @default(auto()) @map("_id") @db.ObjectId
  customer   Customer   @relation(fields: [customerId], references: [id])
  customerId String     @unique @db.ObjectId
  items      CartItem[]
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt

  @@map("carts")
}

model CartItem {
  id                String         @id @default(auto()) @map("_id") @db.ObjectId
  cart              Cart           @relation(fields: [cartId], references: [id])
  cartId            String         @db.ObjectId
  productVariant    ProductVariant @relation(fields: [productVariantId], references: [id])
  productVariantId  String         @db.ObjectId
  quantity          Int
  addedAt           DateTime       @default(now())

  @@unique([cartId, productVariantId])
  @@map("cart_items")
}

model Wishlist {
  id         String         @id @default(auto()) @map("_id") @db.ObjectId
  customer   Customer       @relation(fields: [customerId], references: [id])
  customerId String         @unique @db.ObjectId
  items      WishlistItem[]
  createdAt  DateTime       @default(now())
  updatedAt  DateTime       @updatedAt

  @@map("wishlists")
}

model WishlistItem {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  wishlist   Wishlist @relation(fields: [wishlistId], references: [id])
  wishlistId String   @db.ObjectId
  product    Product  @relation(fields: [productId], references: [id])
  productId  String   @db.ObjectId
  addedAt    DateTime @default(now())

  @@unique([wishlistId, productId])
  @@map("wishlist_items")
}

// Enums
enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}

enum AddressType {
  SHIPPING
  BILLING
}

enum Gender {
  MALE
  FEMALE
  OTHER
  PREFER_NOT_TO_SAY
}


