import { builder } from './builder'

// Custom error types
export class ValidationError extends <PERSON><PERSON>r {
  constructor(message: string, public field?: string) {
    super(message)
    this.name = 'ValidationError'
  }
}

export class NotFoundError extends Error {
  constructor(resource: string, identifier?: string) {
    super(`${resource}${identifier ? ` with identifier '${identifier}'` : ''} not found`)
    this.name = 'NotFoundError'
  }
}

export class ConflictError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'ConflictError'
  }
}

// Utility functions
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validateSlug(slug: string): boolean {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/
  return slugRegex.test(slug)
}

export function validateSKU(sku: string): boolean {
  const skuRegex = /^[A-Z0-9\-_]+$/
  return skuRegex.test(sku)
}

export function validatePrice(price: number): boolean {
  return price > 0 && Number.isFinite(price)
}

export function validateQuantity(quantity: number): boolean {
  return Number.isInteger(quantity) && quantity >= 0
}

// Input validation helpers
export function validateRequiredString(value: string | null | undefined, fieldName: string): string {
  if (!value || value.trim().length === 0) {
    throw new ValidationError(`${fieldName} is required`, fieldName)
  }
  return value.trim()
}

export function validateEmailField(email: string, fieldName: string = 'email'): string {
  const trimmedEmail = validateRequiredString(email, fieldName)
  if (!validateEmail(trimmedEmail)) {
    throw new ValidationError(`Invalid ${fieldName} format`, fieldName)
  }
  return trimmedEmail.toLowerCase()
}

export function validateSlugField(slug: string, fieldName: string = 'slug'): string {
  const trimmedSlug = validateRequiredString(slug, fieldName)
  if (!validateSlug(trimmedSlug)) {
    throw new ValidationError(`Invalid ${fieldName} format. Use lowercase letters, numbers, and hyphens only`, fieldName)
  }
  return trimmedSlug
}

export function validatePriceField(price: number, fieldName: string = 'price'): number {
  if (!validatePrice(price)) {
    throw new ValidationError(`${fieldName} must be a positive number`, fieldName)
  }
  return price
}

// Error handling middleware
export function handlePrismaError(error: any): never {
  if (error.code === 'P2002') {
    // Unique constraint violation
    const field = error.meta?.target?.[0] || 'field'
    throw new ConflictError(`A record with this ${field} already exists`)
  }

  if (error.code === 'P2025') {
    // Record not found
    throw new NotFoundError('Record')
  }

  if (error.code === 'P2003') {
    // Foreign key constraint violation
    throw new ValidationError('Referenced record does not exist')
  }

  // Re-throw other errors
  throw error
}