import { GraphQLError } from 'graphql'
import { Prisma } from '@prisma/client'

export class AppError extends Error {
  constructor(
    message: string,
    public code: string = 'INTERNAL_ERROR',
    public statusCode: number = 500
  ) {
    super(message)
    this.name = 'AppError'
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 'VALIDATION_ERROR', 400)
    this.name = 'ValidationError'
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 'NOT_FOUND', 404)
    this.name = 'NotFoundError'
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 'CONFLICT', 409)
    this.name = 'ConflictError'
  }
}

export class InsufficientStockError extends AppError {
  constructor(sku?: string) {
    const message = sku 
      ? `Insufficient stock for product variant ${sku}`
      : 'Insufficient stock'
    super(message, 'INSUFFICIENT_STOCK', 400)
    this.name = 'InsufficientStockError'
  }
}

export function handlePrismaError(error: any): never {
  console.error('Prisma error:', error)

  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        // Unique constraint violation
        const field = error.meta?.target as string[]
        const fieldName = field?.[0] || 'field'
        throw new ConflictError(`A record with this ${fieldName} already exists`)
      
      case 'P2025':
        // Record not found
        throw new NotFoundError()
      
      case 'P2003':
        // Foreign key constraint violation
        throw new ValidationError('Referenced record does not exist')
      
      case 'P2014':
        // Required relation missing
        throw new ValidationError('Missing required relation')
      
      default:
        throw new AppError('Database operation failed', 'DATABASE_ERROR', 500)
    }
  }

  if (error instanceof Prisma.PrismaClientValidationError) {
    throw new ValidationError('Invalid data provided')
  }

  if (error instanceof Prisma.PrismaClientUnknownRequestError) {
    throw new AppError('Database connection failed', 'DATABASE_CONNECTION_ERROR', 503)
  }

  // Re-throw if it's already an AppError
  if (error instanceof AppError) {
    throw error
  }

  // Default error
  throw new AppError('An unexpected error occurred', 'INTERNAL_ERROR', 500)
}

export function formatGraphQLError(error: any) {
  console.error('GraphQL Error:', error)

  if (error instanceof AppError) {
    return new GraphQLError(error.message, {
      extensions: {
        code: error.code,
        statusCode: error.statusCode,
      },
    })
  }

  if (error instanceof Prisma.PrismaClientKnownRequestError ||
      error instanceof Prisma.PrismaClientValidationError ||
      error instanceof Prisma.PrismaClientUnknownRequestError) {
    try {
      handlePrismaError(error)
    } catch (appError) {
      return formatGraphQLError(appError)
    }
  }

  // Default error formatting
  return new GraphQLError('An unexpected error occurred', {
    extensions: {
      code: 'INTERNAL_ERROR',
      statusCode: 500,
    },
  })
}
