import { create<PERSON>oga } from 'graphql-yoga'
import { schema } from '../graphql/schema'
import { formatGraphQLError } from '../lib/errors'
import { NextRequest } from 'next/server'

const yoga = createYoga({
  schema,
  
  // GraphQL endpoint
  graphqlEndpoint: '/api/graphql',
  
  // Enable GraphiQL in development
  graphiql: process.env.NODE_ENV === 'development',
  
  // Format errors
  maskedErrors: {
    maskError: formatGraphQLError,
    isDev: process.env.NODE_ENV === 'development',
  },
  
  // CORS configuration
  cors: {
    origin: process.env.NODE_ENV === 'development' 
      ? ['http://localhost:3000', 'http://127.0.0.1:3000']
      : [process.env.FRONTEND_URL || 'https://yourdomain.com'],
    credentials: true,
  },
  
  // Context function
  context: async ({ request }: { request: NextRequest }) => {
    // You can add authentication, user context, etc. here
    return {
      request,
      // Add any context you need in your resolvers
    }
  },
  
  // Logging
  logging: process.env.NODE_ENV === 'development',
  
  // Health check endpoint
  healthCheckEndpoint: '/api/health',
})

export { yoga as graphqlHandler }
