import { builder } from './builder'

// Traditional pagination input
export const PaginationInput = builder.inputType('PaginationInput', {
  fields: (t) => ({
    page: t.int({ required: false, description: 'Page number (1-based)' }),
    pageSize: t.int({ required: false, description: 'Number of items per page' }),
    offset: t.int({ required: false, description: 'Number of items to skip' }),
    skip: t.int({ required: false, description: 'Alias for offset' }),
  }),
})

// Cursor-based pagination input
export const CursorPaginationInput = builder.inputType('CursorPaginationInput', {
  fields: (t) => ({
    first: t.int({ required: false, description: 'Number of items to fetch from start' }),
    after: t.string({ required: false, description: 'Cursor to start after' }),
    last: t.int({ required: false, description: 'Number of items to fetch from end' }),
    before: t.string({ required: false, description: 'Cursor to end before' }),
  }),
})

// Sort direction enum
export const SortDirection = builder.enumType('SortDirection', {
  values: {
    ASC: { value: 'asc' },
    DESC: { value: 'desc' },
  },
})

// Page info for cursor pagination
export const PageInfo = builder.objectType('PageInfo', {
  fields: (t) => ({
    hasNextPage: t.boolean({ description: 'Whether there are more items after the current page' }),
    hasPreviousPage: t.boolean({ description: 'Whether there are more items before the current page' }),
    startCursor: t.string({ nullable: true, description: 'Cursor of the first item' }),
    endCursor: t.string({ nullable: true, description: 'Cursor of the last item' }),
  }),
})

// Traditional pagination info
export const PaginationInfo = builder.objectType('PaginationInfo', {
  fields: (t) => ({
    currentPage: t.int({ description: 'Current page number' }),
    pageSize: t.int({ description: 'Number of items per page' }),
    totalPages: t.int({ description: 'Total number of pages' }),
    totalCount: t.int({ description: 'Total number of items' }),
    hasNextPage: t.boolean({ description: 'Whether there is a next page' }),
    hasPreviousPage: t.boolean({ description: 'Whether there is a previous page' }),
  }),
})

// Utility functions for pagination
export interface TraditionalPaginationArgs {
  page?: number | null
  pageSize?: number | null
  offset?: number | null
  skip?: number | null
}

export interface CursorPaginationArgs {
  first?: number | null
  after?: string | null
  last?: number | null
  before?: string | null
}

export function calculateTraditionalPagination(args: TraditionalPaginationArgs, defaultPageSize = 20) {
  const pageSize = args.pageSize || defaultPageSize
  const page = args.page || 1
  const offset = args.offset || args.skip || (page - 1) * pageSize
  
  return {
    take: pageSize,
    skip: offset,
    page,
    pageSize,
  }
}

export function calculateCursorPagination(args: CursorPaginationArgs, defaultLimit = 20) {
  const { first, after, last, before } = args
  
  if (first && last) {
    throw new Error('Cannot specify both first and last')
  }
  
  if (after && before) {
    throw new Error('Cannot specify both after and before')
  }
  
  const limit = first || last || defaultLimit
  
  return {
    take: limit,
    cursor: after || before ? { id: after || before } : undefined,
    skip: after || before ? 1 : 0, // Skip the cursor item itself
  }
}

export function createTraditionalPaginationInfo(
  totalCount: number,
  page: number,
  pageSize: number
) {
  const totalPages = Math.ceil(totalCount / pageSize)
  
  return {
    currentPage: page,
    pageSize,
    totalPages,
    totalCount,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
  }
}

export function createCursorPageInfo(
  items: any[],
  hasMore: boolean,
  hasPrevious: boolean
) {
  const startCursor = items.length > 0 ? items[0].id : null
  const endCursor = items.length > 0 ? items[items.length - 1].id : null
  
  return {
    hasNextPage: hasMore,
    hasPreviousPage: hasPrevious,
    startCursor,
    endCursor,
  }
}

// Generic connection type creator
export function createConnection<T>(
  name: string,
  nodeType: T
) {
  const Edge = builder.objectType(`${name}Edge`, {
    fields: (t) => ({
      node: t.field({ type: nodeType }),
      cursor: t.string(),
    }),
  })
  
  const Connection = builder.objectType(`${name}Connection`, {
    fields: (t) => ({
      edges: t.field({ type: [Edge] }),
      pageInfo: t.field({ type: PageInfo }),
      totalCount: t.int({ nullable: true }),
    }),
  })
  
  return { Edge, Connection }
}
