import { ValidationError } from './errors'

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

export const validateSKU = (sku: string): boolean => {
  // SKU should be alphanumeric with hyphens/underscores, 3-50 characters
  const skuRegex = /^[a-zA-Z0-9\-_]{3,50}$/
  return skuRegex.test(sku)
}

export const validateHexColor = (hex: string): boolean => {
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  return hexRegex.test(hex)
}

export const validatePrice = (price: number): boolean => {
  return price >= 0 && price <= 999999.99
}

export const validateQuantity = (quantity: number): boolean => {
  return Number.isInteger(quantity) && quantity >= 0 && quantity <= 999999
}

export const validateDateRange = (startDate: Date, endDate: Date): boolean => {
  return startDate < endDate
}

export const validateDiscount = (discountPercent?: number, discountAmount?: number): void => {
  if (!discountPercent && !discountAmount) {
    throw new ValidationError('Either discountPercent or discountAmount must be provided')
  }

  if (discountPercent && (discountPercent < 0 || discountPercent > 100)) {
    throw new ValidationError('Discount percentage must be between 0 and 100')
  }

  if (discountAmount && discountAmount < 0) {
    throw new ValidationError('Discount amount must be positive')
  }
}

export const validateOrderItems = (items: Array<{ productVariantId: string; quantity: number }>): void => {
  if (!items || items.length === 0) {
    throw new ValidationError('Order must contain at least one item')
  }

  for (const item of items) {
    if (!item.productVariantId) {
      throw new ValidationError('Product variant ID is required for all items')
    }
    
    if (!validateQuantity(item.quantity) || item.quantity === 0) {
      throw new ValidationError('Item quantity must be a positive integer')
    }
  }

  // Check for duplicate items
  const variantIds = items.map(item => item.productVariantId)
  const uniqueVariantIds = new Set(variantIds)
  if (variantIds.length !== uniqueVariantIds.size) {
    throw new ValidationError('Duplicate items are not allowed in an order')
  }
}

export const sanitizeString = (str: string): string => {
  return str.trim().replace(/\s+/g, ' ')
}

export const sanitizeName = (name: string): string => {
  const sanitized = sanitizeString(name)
  if (sanitized.length < 1 || sanitized.length > 255) {
    throw new ValidationError('Name must be between 1 and 255 characters')
  }
  return sanitized
}

export const sanitizeDescription = (description: string): string => {
  const sanitized = sanitizeString(description)
  if (sanitized.length > 5000) {
    throw new ValidationError('Description must not exceed 5000 characters')
  }
  return sanitized
}

export const validateImageUrl = (url: string): boolean => {
  try {
    new URL(url)
    // Check if it's likely an image URL
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
    const lowercaseUrl = url.toLowerCase()
    return imageExtensions.some(ext => lowercaseUrl.includes(ext)) || 
           lowercaseUrl.includes('image') ||
           lowercaseUrl.includes('img')
  } catch {
    return false
  }
}

export const validatePagination = (first?: number, last?: number): void => {
  if (first && first < 0) {
    throw new ValidationError('First must be non-negative')
  }
  
  if (last && last < 0) {
    throw new ValidationError('Last must be non-negative')
  }
  
  if (first && first > 100) {
    throw new ValidationError('First cannot exceed 100')
  }
  
  if (last && last > 100) {
    throw new ValidationError('Last cannot exceed 100')
  }
}
