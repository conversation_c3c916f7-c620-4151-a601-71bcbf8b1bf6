import { builder } from './builder'

// String filter operations
export const StringFilter = builder.inputType('StringFilter', {
  fields: (t) => ({
    equals: t.string({ required: false }),
    not: t.string({ required: false }),
    in: t.stringList({ required: false }),
    notIn: t.stringList({ required: false }),
    contains: t.string({ required: false }),
    startsWith: t.string({ required: false }),
    endsWith: t.string({ required: false }),
    mode: t.field({ type: 'QueryMode', required: false }),
  }),
})

// Number filter operations
export const IntFilter = builder.inputType('IntFilter', {
  fields: (t) => ({
    equals: t.int({ required: false }),
    not: t.int({ required: false }),
    in: t.intList({ required: false }),
    notIn: t.intList({ required: false }),
    lt: t.int({ required: false }),
    lte: t.int({ required: false }),
    gt: t.int({ required: false }),
    gte: t.int({ required: false }),
  }),
})

// Float filter operations
export const FloatFilter = builder.inputType('FloatFilter', {
  fields: (t) => ({
    equals: t.float({ required: false }),
    not: t.float({ required: false }),
    in: t.floatList({ required: false }),
    notIn: t.floatList({ required: false }),
    lt: t.float({ required: false }),
    lte: t.float({ required: false }),
    gt: t.float({ required: false }),
    gte: t.float({ required: false }),
  }),
})

// Boolean filter operations
export const BooleanFilter = builder.inputType('BooleanFilter', {
  fields: (t) => ({
    equals: t.boolean({ required: false }),
    not: t.boolean({ required: false }),
  }),
})

// DateTime filter operations
export const DateTimeFilter = builder.inputType('DateTimeFilter', {
  fields: (t) => ({
    equals: t.field({ type: 'DateTime', required: false }),
    not: t.field({ type: 'DateTime', required: false }),
    in: t.field({ type: ['DateTime'], required: false }),
    notIn: t.field({ type: ['DateTime'], required: false }),
    lt: t.field({ type: 'DateTime', required: false }),
    lte: t.field({ type: 'DateTime', required: false }),
    gt: t.field({ type: 'DateTime', required: false }),
    gte: t.field({ type: 'DateTime', required: false }),
  }),
})

// Query mode enum for string operations
export const QueryMode = builder.enumType('QueryMode', {
  values: {
    DEFAULT: { value: 'default' },
    INSENSITIVE: { value: 'insensitive' },
  },
})

// Generic list filter
export const StringListFilter = builder.inputType('StringListFilter', {
  fields: (t) => ({
    equals: t.stringList({ required: false }),
    has: t.string({ required: false }),
    hasEvery: t.stringList({ required: false }),
    hasSome: t.stringList({ required: false }),
    isEmpty: t.boolean({ required: false }),
  }),
})

// Order by direction
export const OrderDirection = builder.enumType('OrderDirection', {
  values: {
    ASC: { value: 'asc' },
    DESC: { value: 'desc' },
  },
})

// Utility function to convert filter input to Prisma where clause
export function convertStringFilter(filter: any) {
  if (!filter) return undefined
  
  const where: any = {}
  
  if (filter.equals !== undefined) where.equals = filter.equals
  if (filter.not !== undefined) where.not = filter.not
  if (filter.in !== undefined) where.in = filter.in
  if (filter.notIn !== undefined) where.notIn = filter.notIn
  if (filter.contains !== undefined) {
    where.contains = filter.contains
    if (filter.mode === 'insensitive') {
      where.mode = 'insensitive'
    }
  }
  if (filter.startsWith !== undefined) {
    where.startsWith = filter.startsWith
    if (filter.mode === 'insensitive') {
      where.mode = 'insensitive'
    }
  }
  if (filter.endsWith !== undefined) {
    where.endsWith = filter.endsWith
    if (filter.mode === 'insensitive') {
      where.mode = 'insensitive'
    }
  }
  
  return Object.keys(where).length > 0 ? where : undefined
}

export function convertNumberFilter(filter: any) {
  if (!filter) return undefined
  
  const where: any = {}
  
  if (filter.equals !== undefined) where.equals = filter.equals
  if (filter.not !== undefined) where.not = filter.not
  if (filter.in !== undefined) where.in = filter.in
  if (filter.notIn !== undefined) where.notIn = filter.notIn
  if (filter.lt !== undefined) where.lt = filter.lt
  if (filter.lte !== undefined) where.lte = filter.lte
  if (filter.gt !== undefined) where.gt = filter.gt
  if (filter.gte !== undefined) where.gte = filter.gte
  
  return Object.keys(where).length > 0 ? where : undefined
}

export function convertBooleanFilter(filter: any) {
  if (!filter) return undefined
  
  const where: any = {}
  
  if (filter.equals !== undefined) where.equals = filter.equals
  if (filter.not !== undefined) where.not = filter.not
  
  return Object.keys(where).length > 0 ? where : undefined
}

export function convertDateTimeFilter(filter: any) {
  if (!filter) return undefined
  
  const where: any = {}
  
  if (filter.equals !== undefined) where.equals = filter.equals
  if (filter.not !== undefined) where.not = filter.not
  if (filter.in !== undefined) where.in = filter.in
  if (filter.notIn !== undefined) where.notIn = filter.notIn
  if (filter.lt !== undefined) where.lt = filter.lt
  if (filter.lte !== undefined) where.lte = filter.lte
  if (filter.gt !== undefined) where.gt = filter.gt
  if (filter.gte !== undefined) where.gte = filter.gte
  
  return Object.keys(where).length > 0 ? where : undefined
}

export function convertStringListFilter(filter: any) {
  if (!filter) return undefined
  
  const where: any = {}
  
  if (filter.equals !== undefined) where.equals = filter.equals
  if (filter.has !== undefined) where.has = filter.has
  if (filter.hasEvery !== undefined) where.hasEvery = filter.hasEvery
  if (filter.hasSome !== undefined) where.hasSome = filter.hasSome
  if (filter.isEmpty !== undefined) where.isEmpty = filter.isEmpty
  
  return Object.keys(where).length > 0 ? where : undefined
}

// Search utility for full-text search across multiple fields
export function createSearchFilter(searchTerm: string, fields: string[]) {
  if (!searchTerm || !fields.length) return undefined
  
  return {
    OR: fields.map(field => ({
      [field]: {
        contains: searchTerm,
        mode: 'insensitive'
      }
    }))
  }
}
