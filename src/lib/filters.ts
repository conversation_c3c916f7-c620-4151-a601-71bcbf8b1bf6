import { builder } from './builder'

// String filter operations
export const StringFilter = builder.inputType('StringFilter', {
  fields: (t) => ({
    equals: t.string({ required: false }),
    not: t.string({ required: false }),
    in: t.stringList({ required: false }),
    notIn: t.stringList({ required: false }),
    contains: t.string({ required: false }),
    startsWith: t.string({ required: false }),
    endsWith: t.string({ required: false }),
    mode: t.field({ type: 'QueryMode', required: false }),
  }),
})

// Number filter operations
export const IntFilter = builder.inputType('IntFilter', {
  fields: (t) => ({
    equals: t.int({ required: false }),
    not: t.int({ required: false }),
    in: t.intList({ required: false }),
    notIn: t.intList({ required: false }),
    lt: t.int({ required: false }),
    lte: t.int({ required: false }),
    gt: t.int({ required: false }),
    gte: t.int({ required: false }),
  }),
})

// Float filter operations
export const FloatFilter = builder.inputType('FloatFilter', {
  fields: (t) => ({
    equals: t.float({ required: false }),
    not: t.float({ required: false }),
    in: t.floatList({ required: false }),
    notIn: t.floatList({ required: false }),
    lt: t.float({ required: false }),
    lte: t.float({ required: false }),
    gt: t.float({ required: false }),
    gte: t.float({ required: false }),
  }),
})

// Boolean filter operations
export const BooleanFilter = builder.inputType('BooleanFilter', {
  fields: (t) => ({
    equals: t.boolean({ required: false }),
    not: t.boolean({ required: false }),
  }),
})

// DateTime filter operations
export const DateTimeFilter = builder.inputType('DateTimeFilter', {
  fields: (t) => ({
    equals: t.field({ type: 'DateTime', required: false }),
    not: t.field({ type: 'DateTime', required: false }),
    in: t.field({ type: ['DateTime'], required: false }),
    notIn: t.field({ type: ['DateTime'], required: false }),
    lt: t.field({ type: 'DateTime', required: false }),
    lte: t.field({ type: 'DateTime', required: false }),
    gt: t.field({ type: 'DateTime', required: false }),
    gte: t.field({ type: 'DateTime', required: false }),
  }),
})

// Query mode enum for string operations
export const QueryMode = builder.enumType('QueryMode', {
  values: {
    DEFAULT: { value: 'default' },
    INSENSITIVE: { value: 'insensitive' },
  },
})

// Generic list filter
export const StringListFilter = builder.inputType('StringListFilter', {
  fields: (t) => ({
    equals: t.stringList({ required: false }),
    has: t.string({ required: false }),
    hasEvery: t.stringList({ required: false }),
    hasSome: t.stringList({ required: false }),
    isEmpty: t.boolean({ required: false }),
  }),
})

// Order by direction
export const OrderDirection = builder.enumType('OrderDirection', {
  values: {
    ASC: { value: 'asc' },
    DESC: { value: 'desc' },
  },
})

// Utility function to convert filter input to Prisma where clause
export function convertStringFilter(filter: unknown) {
  if (!filter) return undefined

  const filterObj = filter as Record<string, unknown>
  const where: Record<string, unknown> = {}
  
  if (filterObj.equals !== undefined) where.equals = filterObj.equals
  if (filterObj.not !== undefined) where.not = filterObj.not
  if (filterObj.in !== undefined) where.in = filterObj.in
  if (filterObj.notIn !== undefined) where.notIn = filterObj.notIn
  if (filterObj.contains !== undefined) {
    where.contains = filterObj.contains
    if (filterObj.mode === 'insensitive') {
      where.mode = 'insensitive'
    }
  }
  if (filterObj.startsWith !== undefined) {
    where.startsWith = filterObj.startsWith
    if (filterObj.mode === 'insensitive') {
      where.mode = 'insensitive'
    }
  }
  if (filterObj.endsWith !== undefined) {
    where.endsWith = filterObj.endsWith
    if (filterObj.mode === 'insensitive') {
      where.mode = 'insensitive'
    }
  }
  
  return Object.keys(where).length > 0 ? where : undefined
}

export function convertNumberFilter(filter: unknown) {
  if (!filter) return undefined

  const filterObj = filter as Record<string, unknown>
  const where: Record<string, unknown> = {}
  
  if (filterObj.equals !== undefined) where.equals = filterObj.equals
  if (filterObj.not !== undefined) where.not = filterObj.not
  if (filterObj.in !== undefined) where.in = filterObj.in
  if (filterObj.notIn !== undefined) where.notIn = filterObj.notIn
  if (filterObj.lt !== undefined) where.lt = filterObj.lt
  if (filterObj.lte !== undefined) where.lte = filterObj.lte
  if (filterObj.gt !== undefined) where.gt = filterObj.gt
  if (filterObj.gte !== undefined) where.gte = filterObj.gte
  
  return Object.keys(where).length > 0 ? where : undefined
}

export function convertBooleanFilter(filter: unknown) {
  if (!filter) return undefined

  const filterObj = filter as Record<string, unknown>
  const where: Record<string, unknown> = {}

  if (filterObj.equals !== undefined) where.equals = filterObj.equals
  if (filterObj.not !== undefined) where.not = filterObj.not
  
  return Object.keys(where).length > 0 ? where : undefined
}

export function convertDateTimeFilter(filter: unknown) {
  if (!filter) return undefined

  const filterObj = filter as Record<string, unknown>
  const where: Record<string, unknown> = {}

  if (filterObj.equals !== undefined) where.equals = filterObj.equals
  if (filterObj.not !== undefined) where.not = filterObj.not
  if (filterObj.in !== undefined) where.in = filterObj.in
  if (filterObj.notIn !== undefined) where.notIn = filterObj.notIn
  if (filterObj.lt !== undefined) where.lt = filterObj.lt
  if (filterObj.lte !== undefined) where.lte = filterObj.lte
  if (filterObj.gt !== undefined) where.gt = filterObj.gt
  if (filterObj.gte !== undefined) where.gte = filterObj.gte
  
  return Object.keys(where).length > 0 ? where : undefined
}

export function convertStringListFilter(filter: unknown) {
  if (!filter) return undefined

  const filterObj = filter as Record<string, unknown>
  const where: Record<string, unknown> = {}

  if (filterObj.equals !== undefined) where.equals = filterObj.equals
  if (filterObj.has !== undefined) where.has = filterObj.has
  if (filterObj.hasEvery !== undefined) where.hasEvery = filterObj.hasEvery
  if (filterObj.hasSome !== undefined) where.hasSome = filterObj.hasSome
  if (filterObj.isEmpty !== undefined) where.isEmpty = filterObj.isEmpty
  
  return Object.keys(where).length > 0 ? where : undefined
}

// Search utility for full-text search across multiple fields
export function createSearchFilter(searchTerm: string, fields: string[]) {
  if (!searchTerm || !fields.length) return undefined
  
  return {
    OR: fields.map(field => ({
      [field]: {
        contains: searchTerm,
        mode: 'insensitive'
      }
    }))
  }
}
