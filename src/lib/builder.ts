import SchemaBuilder from '@pothos/core'
import PrismaPlugin from '@pothos/plugin-prisma'
import RelayPlugin from '@pothos/plugin-relay'
import ValidationPlugin from '@pothos/plugin-validation'
import SimpleObjectsPlugin from '@pothos/plugin-simple-objects'
import { prisma } from './prisma'
import type { Prisma } from '@prisma/client'

export const builder = new SchemaBuilder<{
  PrismaTypes: Prisma.TypeMap['model']
  Scalars: {
    DateTime: {
      Input: Date
      Output: Date
    }
  }
  DefaultInputFieldRequiredness: true
}>({
  plugins: [PrismaPlugin, RelayPlugin, ValidationPlugin, SimpleObjectsPlugin],
  prisma: {
    client: prisma,
    filterConnectionTotalCount: true,
    onUnusedQuery: process.env.NODE_ENV === 'production' ? null : 'warn',
  },
  relayOptions: {
    clientMutationId: 'omit',
    cursorType: 'String',
  },
  defaultInputFieldRequiredness: true,
})

// Add scalar types
builder.scalarType('DateTime', {
  serialize: (date) => date.toISOString(),
  parseValue: (value) => {
    if (typeof value === 'string') {
      return new Date(value)
    }
    throw new Error('Invalid DateTime value')
  },
})

// Add Query and Mutation root types
builder.queryType({})
builder.mutationType({})
