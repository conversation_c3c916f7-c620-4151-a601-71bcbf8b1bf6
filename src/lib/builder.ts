/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import SchemaBuilder from '@pothos/core';
import PrismaPlugin from '@pothos/plugin-prisma';
import RelayPlugin from '@pothos/plugin-relay';
import ValidationPlugin from '@pothos/plugin-validation';
import ErrorsPlugin from '@pothos/plugin-errors';
import type PrismaTypes from '../generated/pothos-types';
import { prisma } from './prisma';
import { Prisma } from '@prisma/client';

export interface Context {
  prisma: typeof prisma;
}

export const builder = new SchemaBuilder<{
  Context: Context;
  PrismaTypes: PrismaTypes;
  Scalars: {
    DateTime: {
      Input: Date;
      Output: Date;
    };
    JSON: {
      Input: any;
      Output: any;
    };
  };
}>({
  plugins: [PrismaPlugin, RelayPlugin, ValidationPlugin, ErrorsPlugin],
  relayOptions: {
    clientMutationId: 'omit',
    cursorType: 'String',
  },
  prisma: {
    client: prisma,
    dmmf: Prisma.dmmf,
    filterConnectionTotalCount: true,
  },
  errorOptions: {
    defaultTypes: [Error],
  },
  validationOptions: {
    validationError: (zodError, args, context, info) => {
      return zodError;
    },
  },
});

// Add global scalar types
builder.scalarType('DateTime', {
  serialize: (date) => date.toISOString(),
  parseValue: (date) => new Date(date),
});

builder.scalarType('JSON', {
  serialize: (value) => value,
  parseValue: (value) => value,
});

builder.queryType({});
builder.mutationType({});
