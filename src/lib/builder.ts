import SchemaBuilder from '@pothos/core'
import PrismaPlugin from '@pothos/plugin-prisma'
import RelayPlugin from '@pothos/plugin-relay'
import ValidationPlugin from '@pothos/plugin-validation'
import ErrorsPlugin from '@pothos/plugin-errors'
import { prisma } from './prisma'
import type PrismaTypes from '@pothos/plugin-prisma/generated'

export const builder = new SchemaBuilder<{
  PrismaTypes: PrismaTypes
  Context: {
    prisma: typeof prisma
  }
  Scalars: {
    DateTime: {
      Input: Date
      Output: Date
    }
    JSON: {
      Input: any
      Output: any
    }
  }
}>({
  plugins: [PrismaPlugin, RelayPlugin, ValidationPlugin, ErrorsPlugin],
  prisma: {
    client: prisma,
    // Use where clause from prismaObject for filtering
    filterConnectionTotalCount: true,
  },
  relay: {
    // These will become the defaults in the next major version
    clientMutationId: 'omit',
    cursorType: 'String',
  },
  errors: {
    defaultTypes: [Error],
  },
})

// Add custom scalars
builder.scalarType('DateTime', {
  serialize: (date) => date.toISOString(),
  parseValue: (value) => {
    if (typeof value === 'string') {
      return new Date(value)
    }
    throw new Error('Invalid date')
  },
})

builder.scalarType('JSON', {
  serialize: (value) => value,
  parseValue: (value) => value,
})

// Base Query and Mutation types
builder.queryType({})
builder.mutationType({})