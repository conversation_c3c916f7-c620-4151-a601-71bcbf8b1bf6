/* eslint-disable */
import type { Prisma, Category, Product, ProductImage, ProductReview, Size, Color, ProductVariant, Discount, VariantDiscount, Customer, Address, Order, OrderItem, Cart, CartItem, Wishlist, WishlistItem } from "/home/<USER>/Desktop/allincloth/node_modules/@prisma/client/index.js";
export default interface PrismaTypes {
    Category: {
        Name: "Category";
        Shape: Category;
        Include: Prisma.CategoryInclude;
        Select: Prisma.CategorySelect;
        OrderBy: Prisma.CategoryOrderByWithRelationInput;
        WhereUnique: Prisma.CategoryWhereUniqueInput;
        Where: Prisma.CategoryWhereInput;
        Create: {};
        Update: {};
        RelationName: "parent" | "children" | "products";
        ListRelations: "children" | "products";
        Relations: {
            parent: {
                Shape: Category | null;
                Name: "Category";
                Nullable: true;
            };
            children: {
                Shape: Category[];
                Name: "Category";
                Nullable: false;
            };
            products: {
                Shape: Product[];
                Name: "Product";
                Nullable: false;
            };
        };
    };
    Product: {
        Name: "Product";
        Shape: Product;
        Include: Prisma.ProductInclude;
        Select: Prisma.ProductSelect;
        OrderBy: Prisma.ProductOrderByWithRelationInput;
        WhereUnique: Prisma.ProductWhereUniqueInput;
        Where: Prisma.ProductWhereInput;
        Create: {};
        Update: {};
        RelationName: "category" | "variants" | "images" | "reviews" | "wishlistItems";
        ListRelations: "variants" | "images" | "reviews" | "wishlistItems";
        Relations: {
            category: {
                Shape: Category;
                Name: "Category";
                Nullable: false;
            };
            variants: {
                Shape: ProductVariant[];
                Name: "ProductVariant";
                Nullable: false;
            };
            images: {
                Shape: ProductImage[];
                Name: "ProductImage";
                Nullable: false;
            };
            reviews: {
                Shape: ProductReview[];
                Name: "ProductReview";
                Nullable: false;
            };
            wishlistItems: {
                Shape: WishlistItem[];
                Name: "WishlistItem";
                Nullable: false;
            };
        };
    };
    ProductImage: {
        Name: "ProductImage";
        Shape: ProductImage;
        Include: Prisma.ProductImageInclude;
        Select: Prisma.ProductImageSelect;
        OrderBy: Prisma.ProductImageOrderByWithRelationInput;
        WhereUnique: Prisma.ProductImageWhereUniqueInput;
        Where: Prisma.ProductImageWhereInput;
        Create: {};
        Update: {};
        RelationName: "product";
        ListRelations: never;
        Relations: {
            product: {
                Shape: Product;
                Name: "Product";
                Nullable: false;
            };
        };
    };
    ProductReview: {
        Name: "ProductReview";
        Shape: ProductReview;
        Include: Prisma.ProductReviewInclude;
        Select: Prisma.ProductReviewSelect;
        OrderBy: Prisma.ProductReviewOrderByWithRelationInput;
        WhereUnique: Prisma.ProductReviewWhereUniqueInput;
        Where: Prisma.ProductReviewWhereInput;
        Create: {};
        Update: {};
        RelationName: "product" | "customer";
        ListRelations: never;
        Relations: {
            product: {
                Shape: Product;
                Name: "Product";
                Nullable: false;
            };
            customer: {
                Shape: Customer;
                Name: "Customer";
                Nullable: false;
            };
        };
    };
    Size: {
        Name: "Size";
        Shape: Size;
        Include: Prisma.SizeInclude;
        Select: Prisma.SizeSelect;
        OrderBy: Prisma.SizeOrderByWithRelationInput;
        WhereUnique: Prisma.SizeWhereUniqueInput;
        Where: Prisma.SizeWhereInput;
        Create: {};
        Update: {};
        RelationName: "variants";
        ListRelations: "variants";
        Relations: {
            variants: {
                Shape: ProductVariant[];
                Name: "ProductVariant";
                Nullable: false;
            };
        };
    };
    Color: {
        Name: "Color";
        Shape: Color;
        Include: Prisma.ColorInclude;
        Select: Prisma.ColorSelect;
        OrderBy: Prisma.ColorOrderByWithRelationInput;
        WhereUnique: Prisma.ColorWhereUniqueInput;
        Where: Prisma.ColorWhereInput;
        Create: {};
        Update: {};
        RelationName: "variants";
        ListRelations: "variants";
        Relations: {
            variants: {
                Shape: ProductVariant[];
                Name: "ProductVariant";
                Nullable: false;
            };
        };
    };
    ProductVariant: {
        Name: "ProductVariant";
        Shape: ProductVariant;
        Include: Prisma.ProductVariantInclude;
        Select: Prisma.ProductVariantSelect;
        OrderBy: Prisma.ProductVariantOrderByWithRelationInput;
        WhereUnique: Prisma.ProductVariantWhereUniqueInput;
        Where: Prisma.ProductVariantWhereInput;
        Create: {};
        Update: {};
        RelationName: "product" | "size" | "color" | "discounts" | "orderItems" | "cartItems";
        ListRelations: "discounts" | "orderItems" | "cartItems";
        Relations: {
            product: {
                Shape: Product;
                Name: "Product";
                Nullable: false;
            };
            size: {
                Shape: Size;
                Name: "Size";
                Nullable: false;
            };
            color: {
                Shape: Color;
                Name: "Color";
                Nullable: false;
            };
            discounts: {
                Shape: VariantDiscount[];
                Name: "VariantDiscount";
                Nullable: false;
            };
            orderItems: {
                Shape: OrderItem[];
                Name: "OrderItem";
                Nullable: false;
            };
            cartItems: {
                Shape: CartItem[];
                Name: "CartItem";
                Nullable: false;
            };
        };
    };
    Discount: {
        Name: "Discount";
        Shape: Discount;
        Include: Prisma.DiscountInclude;
        Select: Prisma.DiscountSelect;
        OrderBy: Prisma.DiscountOrderByWithRelationInput;
        WhereUnique: Prisma.DiscountWhereUniqueInput;
        Where: Prisma.DiscountWhereInput;
        Create: {};
        Update: {};
        RelationName: "variants" | "orders";
        ListRelations: "variants" | "orders";
        Relations: {
            variants: {
                Shape: VariantDiscount[];
                Name: "VariantDiscount";
                Nullable: false;
            };
            orders: {
                Shape: Order[];
                Name: "Order";
                Nullable: false;
            };
        };
    };
    VariantDiscount: {
        Name: "VariantDiscount";
        Shape: VariantDiscount;
        Include: Prisma.VariantDiscountInclude;
        Select: Prisma.VariantDiscountSelect;
        OrderBy: Prisma.VariantDiscountOrderByWithRelationInput;
        WhereUnique: Prisma.VariantDiscountWhereUniqueInput;
        Where: Prisma.VariantDiscountWhereInput;
        Create: {};
        Update: {};
        RelationName: "productVariant" | "discount";
        ListRelations: never;
        Relations: {
            productVariant: {
                Shape: ProductVariant;
                Name: "ProductVariant";
                Nullable: false;
            };
            discount: {
                Shape: Discount;
                Name: "Discount";
                Nullable: false;
            };
        };
    };
    Customer: {
        Name: "Customer";
        Shape: Customer;
        Include: Prisma.CustomerInclude;
        Select: Prisma.CustomerSelect;
        OrderBy: Prisma.CustomerOrderByWithRelationInput;
        WhereUnique: Prisma.CustomerWhereUniqueInput;
        Where: Prisma.CustomerWhereInput;
        Create: {};
        Update: {};
        RelationName: "addresses" | "orders" | "reviews" | "cart" | "wishlist";
        ListRelations: "addresses" | "orders" | "reviews";
        Relations: {
            addresses: {
                Shape: Address[];
                Name: "Address";
                Nullable: false;
            };
            orders: {
                Shape: Order[];
                Name: "Order";
                Nullable: false;
            };
            reviews: {
                Shape: ProductReview[];
                Name: "ProductReview";
                Nullable: false;
            };
            cart: {
                Shape: Cart | null;
                Name: "Cart";
                Nullable: true;
            };
            wishlist: {
                Shape: Wishlist | null;
                Name: "Wishlist";
                Nullable: true;
            };
        };
    };
    Address: {
        Name: "Address";
        Shape: Address;
        Include: Prisma.AddressInclude;
        Select: Prisma.AddressSelect;
        OrderBy: Prisma.AddressOrderByWithRelationInput;
        WhereUnique: Prisma.AddressWhereUniqueInput;
        Where: Prisma.AddressWhereInput;
        Create: {};
        Update: {};
        RelationName: "customer";
        ListRelations: never;
        Relations: {
            customer: {
                Shape: Customer;
                Name: "Customer";
                Nullable: false;
            };
        };
    };
    Order: {
        Name: "Order";
        Shape: Order;
        Include: Prisma.OrderInclude;
        Select: Prisma.OrderSelect;
        OrderBy: Prisma.OrderOrderByWithRelationInput;
        WhereUnique: Prisma.OrderWhereUniqueInput;
        Where: Prisma.OrderWhereInput;
        Create: {};
        Update: {};
        RelationName: "customer" | "discount" | "items";
        ListRelations: "items";
        Relations: {
            customer: {
                Shape: Customer;
                Name: "Customer";
                Nullable: false;
            };
            discount: {
                Shape: Discount | null;
                Name: "Discount";
                Nullable: true;
            };
            items: {
                Shape: OrderItem[];
                Name: "OrderItem";
                Nullable: false;
            };
        };
    };
    OrderItem: {
        Name: "OrderItem";
        Shape: OrderItem;
        Include: Prisma.OrderItemInclude;
        Select: Prisma.OrderItemSelect;
        OrderBy: Prisma.OrderItemOrderByWithRelationInput;
        WhereUnique: Prisma.OrderItemWhereUniqueInput;
        Where: Prisma.OrderItemWhereInput;
        Create: {};
        Update: {};
        RelationName: "order" | "productVariant";
        ListRelations: never;
        Relations: {
            order: {
                Shape: Order;
                Name: "Order";
                Nullable: false;
            };
            productVariant: {
                Shape: ProductVariant;
                Name: "ProductVariant";
                Nullable: false;
            };
        };
    };
    Cart: {
        Name: "Cart";
        Shape: Cart;
        Include: Prisma.CartInclude;
        Select: Prisma.CartSelect;
        OrderBy: Prisma.CartOrderByWithRelationInput;
        WhereUnique: Prisma.CartWhereUniqueInput;
        Where: Prisma.CartWhereInput;
        Create: {};
        Update: {};
        RelationName: "customer" | "items";
        ListRelations: "items";
        Relations: {
            customer: {
                Shape: Customer;
                Name: "Customer";
                Nullable: false;
            };
            items: {
                Shape: CartItem[];
                Name: "CartItem";
                Nullable: false;
            };
        };
    };
    CartItem: {
        Name: "CartItem";
        Shape: CartItem;
        Include: Prisma.CartItemInclude;
        Select: Prisma.CartItemSelect;
        OrderBy: Prisma.CartItemOrderByWithRelationInput;
        WhereUnique: Prisma.CartItemWhereUniqueInput;
        Where: Prisma.CartItemWhereInput;
        Create: {};
        Update: {};
        RelationName: "cart" | "productVariant";
        ListRelations: never;
        Relations: {
            cart: {
                Shape: Cart;
                Name: "Cart";
                Nullable: false;
            };
            productVariant: {
                Shape: ProductVariant;
                Name: "ProductVariant";
                Nullable: false;
            };
        };
    };
    Wishlist: {
        Name: "Wishlist";
        Shape: Wishlist;
        Include: Prisma.WishlistInclude;
        Select: Prisma.WishlistSelect;
        OrderBy: Prisma.WishlistOrderByWithRelationInput;
        WhereUnique: Prisma.WishlistWhereUniqueInput;
        Where: Prisma.WishlistWhereInput;
        Create: {};
        Update: {};
        RelationName: "customer" | "items";
        ListRelations: "items";
        Relations: {
            customer: {
                Shape: Customer;
                Name: "Customer";
                Nullable: false;
            };
            items: {
                Shape: WishlistItem[];
                Name: "WishlistItem";
                Nullable: false;
            };
        };
    };
    WishlistItem: {
        Name: "WishlistItem";
        Shape: WishlistItem;
        Include: Prisma.WishlistItemInclude;
        Select: Prisma.WishlistItemSelect;
        OrderBy: Prisma.WishlistItemOrderByWithRelationInput;
        WhereUnique: Prisma.WishlistItemWhereUniqueInput;
        Where: Prisma.WishlistItemWhereInput;
        Create: {};
        Update: {};
        RelationName: "wishlist" | "product";
        ListRelations: never;
        Relations: {
            wishlist: {
                Shape: Wishlist;
                Name: "Wishlist";
                Nullable: false;
            };
            product: {
                Shape: Product;
                Name: "Product";
                Nullable: false;
            };
        };
    };
}