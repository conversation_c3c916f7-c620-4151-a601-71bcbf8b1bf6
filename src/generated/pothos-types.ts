/* eslint-disable */
import type { Prisma, Customer, Wishlist, WishlistItem, Review, PromoCode, PromoCodeUsage, Tag, ProductTag, PaymentMethod, PaymentTransaction, Order, OrderItem, Product, ProductImage, Size, Color, ProductVariant, Discount, VariantDiscount, Category } from "/home/<USER>/Desktop/allincloth/node_modules/@prisma/client/index.js";
export default interface PrismaTypes {
    Customer: {
        Name: "Customer";
        Shape: Customer;
        Include: Prisma.CustomerInclude;
        Select: Prisma.CustomerSelect;
        OrderBy: Prisma.CustomerOrderByWithRelationInput;
        WhereUnique: Prisma.CustomerWhereUniqueInput;
        Where: Prisma.CustomerWhereInput;
        Create: {};
        Update: {};
        RelationName: "wishlist" | "reviews" | "orders" | "paymentMethods" | "promoCodeUsages";
        ListRelations: "reviews" | "orders" | "paymentMethods" | "promoCodeUsages";
        Relations: {
            wishlist: {
                Shape: Wishlist | null;
                Name: "Wishlist";
                Nullable: true;
            };
            reviews: {
                Shape: Review[];
                Name: "Review";
                Nullable: false;
            };
            orders: {
                Shape: Order[];
                Name: "Order";
                Nullable: false;
            };
            paymentMethods: {
                Shape: PaymentMethod[];
                Name: "PaymentMethod";
                Nullable: false;
            };
            promoCodeUsages: {
                Shape: PromoCodeUsage[];
                Name: "PromoCodeUsage";
                Nullable: false;
            };
        };
    };
    Wishlist: {
        Name: "Wishlist";
        Shape: Wishlist;
        Include: Prisma.WishlistInclude;
        Select: Prisma.WishlistSelect;
        OrderBy: Prisma.WishlistOrderByWithRelationInput;
        WhereUnique: Prisma.WishlistWhereUniqueInput;
        Where: Prisma.WishlistWhereInput;
        Create: {};
        Update: {};
        RelationName: "customer" | "items";
        ListRelations: "items";
        Relations: {
            customer: {
                Shape: Customer;
                Name: "Customer";
                Nullable: false;
            };
            items: {
                Shape: WishlistItem[];
                Name: "WishlistItem";
                Nullable: false;
            };
        };
    };
    WishlistItem: {
        Name: "WishlistItem";
        Shape: WishlistItem;
        Include: Prisma.WishlistItemInclude;
        Select: Prisma.WishlistItemSelect;
        OrderBy: Prisma.WishlistItemOrderByWithRelationInput;
        WhereUnique: Prisma.WishlistItemWhereUniqueInput;
        Where: Prisma.WishlistItemWhereInput;
        Create: {};
        Update: {};
        RelationName: "wishlist" | "productVariant";
        ListRelations: never;
        Relations: {
            wishlist: {
                Shape: Wishlist;
                Name: "Wishlist";
                Nullable: false;
            };
            productVariant: {
                Shape: ProductVariant;
                Name: "ProductVariant";
                Nullable: false;
            };
        };
    };
    Review: {
        Name: "Review";
        Shape: Review;
        Include: Prisma.ReviewInclude;
        Select: Prisma.ReviewSelect;
        OrderBy: Prisma.ReviewOrderByWithRelationInput;
        WhereUnique: Prisma.ReviewWhereUniqueInput;
        Where: Prisma.ReviewWhereInput;
        Create: {};
        Update: {};
        RelationName: "customer" | "product";
        ListRelations: never;
        Relations: {
            customer: {
                Shape: Customer;
                Name: "Customer";
                Nullable: false;
            };
            product: {
                Shape: Product;
                Name: "Product";
                Nullable: false;
            };
        };
    };
    PromoCode: {
        Name: "PromoCode";
        Shape: PromoCode;
        Include: Prisma.PromoCodeInclude;
        Select: Prisma.PromoCodeSelect;
        OrderBy: Prisma.PromoCodeOrderByWithRelationInput;
        WhereUnique: Prisma.PromoCodeWhereUniqueInput;
        Where: Prisma.PromoCodeWhereInput;
        Create: {};
        Update: {};
        RelationName: "usages";
        ListRelations: "usages";
        Relations: {
            usages: {
                Shape: PromoCodeUsage[];
                Name: "PromoCodeUsage";
                Nullable: false;
            };
        };
    };
    PromoCodeUsage: {
        Name: "PromoCodeUsage";
        Shape: PromoCodeUsage;
        Include: Prisma.PromoCodeUsageInclude;
        Select: Prisma.PromoCodeUsageSelect;
        OrderBy: Prisma.PromoCodeUsageOrderByWithRelationInput;
        WhereUnique: Prisma.PromoCodeUsageWhereUniqueInput;
        Where: Prisma.PromoCodeUsageWhereInput;
        Create: {};
        Update: {};
        RelationName: "promoCode" | "customer";
        ListRelations: never;
        Relations: {
            promoCode: {
                Shape: PromoCode;
                Name: "PromoCode";
                Nullable: false;
            };
            customer: {
                Shape: Customer;
                Name: "Customer";
                Nullable: false;
            };
        };
    };
    Tag: {
        Name: "Tag";
        Shape: Tag;
        Include: Prisma.TagInclude;
        Select: Prisma.TagSelect;
        OrderBy: Prisma.TagOrderByWithRelationInput;
        WhereUnique: Prisma.TagWhereUniqueInput;
        Where: Prisma.TagWhereInput;
        Create: {};
        Update: {};
        RelationName: "productTags";
        ListRelations: "productTags";
        Relations: {
            productTags: {
                Shape: ProductTag[];
                Name: "ProductTag";
                Nullable: false;
            };
        };
    };
    ProductTag: {
        Name: "ProductTag";
        Shape: ProductTag;
        Include: Prisma.ProductTagInclude;
        Select: Prisma.ProductTagSelect;
        OrderBy: Prisma.ProductTagOrderByWithRelationInput;
        WhereUnique: Prisma.ProductTagWhereUniqueInput;
        Where: Prisma.ProductTagWhereInput;
        Create: {};
        Update: {};
        RelationName: "product" | "tag";
        ListRelations: never;
        Relations: {
            product: {
                Shape: Product;
                Name: "Product";
                Nullable: false;
            };
            tag: {
                Shape: Tag;
                Name: "Tag";
                Nullable: false;
            };
        };
    };
    PaymentMethod: {
        Name: "PaymentMethod";
        Shape: PaymentMethod;
        Include: Prisma.PaymentMethodInclude;
        Select: Prisma.PaymentMethodSelect;
        OrderBy: Prisma.PaymentMethodOrderByWithRelationInput;
        WhereUnique: Prisma.PaymentMethodWhereUniqueInput;
        Where: Prisma.PaymentMethodWhereInput;
        Create: {};
        Update: {};
        RelationName: "customer" | "transactions";
        ListRelations: "transactions";
        Relations: {
            customer: {
                Shape: Customer;
                Name: "Customer";
                Nullable: false;
            };
            transactions: {
                Shape: PaymentTransaction[];
                Name: "PaymentTransaction";
                Nullable: false;
            };
        };
    };
    PaymentTransaction: {
        Name: "PaymentTransaction";
        Shape: PaymentTransaction;
        Include: Prisma.PaymentTransactionInclude;
        Select: Prisma.PaymentTransactionSelect;
        OrderBy: Prisma.PaymentTransactionOrderByWithRelationInput;
        WhereUnique: Prisma.PaymentTransactionWhereUniqueInput;
        Where: Prisma.PaymentTransactionWhereInput;
        Create: {};
        Update: {};
        RelationName: "paymentMethod" | "order";
        ListRelations: never;
        Relations: {
            paymentMethod: {
                Shape: PaymentMethod;
                Name: "PaymentMethod";
                Nullable: false;
            };
            order: {
                Shape: Order | null;
                Name: "Order";
                Nullable: true;
            };
        };
    };
    Order: {
        Name: "Order";
        Shape: Order;
        Include: Prisma.OrderInclude;
        Select: Prisma.OrderSelect;
        OrderBy: Prisma.OrderOrderByWithRelationInput;
        WhereUnique: Prisma.OrderWhereUniqueInput;
        Where: Prisma.OrderWhereInput;
        Create: {};
        Update: {};
        RelationName: "customer" | "items" | "paymentTransaction";
        ListRelations: "items";
        Relations: {
            customer: {
                Shape: Customer;
                Name: "Customer";
                Nullable: false;
            };
            items: {
                Shape: OrderItem[];
                Name: "OrderItem";
                Nullable: false;
            };
            paymentTransaction: {
                Shape: PaymentTransaction | null;
                Name: "PaymentTransaction";
                Nullable: true;
            };
        };
    };
    OrderItem: {
        Name: "OrderItem";
        Shape: OrderItem;
        Include: Prisma.OrderItemInclude;
        Select: Prisma.OrderItemSelect;
        OrderBy: Prisma.OrderItemOrderByWithRelationInput;
        WhereUnique: Prisma.OrderItemWhereUniqueInput;
        Where: Prisma.OrderItemWhereInput;
        Create: {};
        Update: {};
        RelationName: "order" | "productVariant";
        ListRelations: never;
        Relations: {
            order: {
                Shape: Order;
                Name: "Order";
                Nullable: false;
            };
            productVariant: {
                Shape: ProductVariant;
                Name: "ProductVariant";
                Nullable: false;
            };
        };
    };
    Product: {
        Name: "Product";
        Shape: Product;
        Include: Prisma.ProductInclude;
        Select: Prisma.ProductSelect;
        OrderBy: Prisma.ProductOrderByWithRelationInput;
        WhereUnique: Prisma.ProductWhereUniqueInput;
        Where: Prisma.ProductWhereInput;
        Create: {};
        Update: {};
        RelationName: "category" | "variants" | "images" | "tags" | "reviews";
        ListRelations: "variants" | "images" | "tags" | "reviews";
        Relations: {
            category: {
                Shape: Category;
                Name: "Category";
                Nullable: false;
            };
            variants: {
                Shape: ProductVariant[];
                Name: "ProductVariant";
                Nullable: false;
            };
            images: {
                Shape: ProductImage[];
                Name: "ProductImage";
                Nullable: false;
            };
            tags: {
                Shape: ProductTag[];
                Name: "ProductTag";
                Nullable: false;
            };
            reviews: {
                Shape: Review[];
                Name: "Review";
                Nullable: false;
            };
        };
    };
    ProductImage: {
        Name: "ProductImage";
        Shape: ProductImage;
        Include: Prisma.ProductImageInclude;
        Select: Prisma.ProductImageSelect;
        OrderBy: Prisma.ProductImageOrderByWithRelationInput;
        WhereUnique: Prisma.ProductImageWhereUniqueInput;
        Where: Prisma.ProductImageWhereInput;
        Create: {};
        Update: {};
        RelationName: "product";
        ListRelations: never;
        Relations: {
            product: {
                Shape: Product;
                Name: "Product";
                Nullable: false;
            };
        };
    };
    Size: {
        Name: "Size";
        Shape: Size;
        Include: Prisma.SizeInclude;
        Select: Prisma.SizeSelect;
        OrderBy: Prisma.SizeOrderByWithRelationInput;
        WhereUnique: Prisma.SizeWhereUniqueInput;
        Where: Prisma.SizeWhereInput;
        Create: {};
        Update: {};
        RelationName: "variants";
        ListRelations: "variants";
        Relations: {
            variants: {
                Shape: ProductVariant[];
                Name: "ProductVariant";
                Nullable: false;
            };
        };
    };
    Color: {
        Name: "Color";
        Shape: Color;
        Include: Prisma.ColorInclude;
        Select: Prisma.ColorSelect;
        OrderBy: Prisma.ColorOrderByWithRelationInput;
        WhereUnique: Prisma.ColorWhereUniqueInput;
        Where: Prisma.ColorWhereInput;
        Create: {};
        Update: {};
        RelationName: "variants";
        ListRelations: "variants";
        Relations: {
            variants: {
                Shape: ProductVariant[];
                Name: "ProductVariant";
                Nullable: false;
            };
        };
    };
    ProductVariant: {
        Name: "ProductVariant";
        Shape: ProductVariant;
        Include: Prisma.ProductVariantInclude;
        Select: Prisma.ProductVariantSelect;
        OrderBy: Prisma.ProductVariantOrderByWithRelationInput;
        WhereUnique: Prisma.ProductVariantWhereUniqueInput;
        Where: Prisma.ProductVariantWhereInput;
        Create: {};
        Update: {};
        RelationName: "product" | "size" | "color" | "discounts" | "wishlistItems" | "orderItems";
        ListRelations: "discounts" | "wishlistItems" | "orderItems";
        Relations: {
            product: {
                Shape: Product;
                Name: "Product";
                Nullable: false;
            };
            size: {
                Shape: Size;
                Name: "Size";
                Nullable: false;
            };
            color: {
                Shape: Color;
                Name: "Color";
                Nullable: false;
            };
            discounts: {
                Shape: VariantDiscount[];
                Name: "VariantDiscount";
                Nullable: false;
            };
            wishlistItems: {
                Shape: WishlistItem[];
                Name: "WishlistItem";
                Nullable: false;
            };
            orderItems: {
                Shape: OrderItem[];
                Name: "OrderItem";
                Nullable: false;
            };
        };
    };
    Discount: {
        Name: "Discount";
        Shape: Discount;
        Include: Prisma.DiscountInclude;
        Select: Prisma.DiscountSelect;
        OrderBy: Prisma.DiscountOrderByWithRelationInput;
        WhereUnique: Prisma.DiscountWhereUniqueInput;
        Where: Prisma.DiscountWhereInput;
        Create: {};
        Update: {};
        RelationName: "variants";
        ListRelations: "variants";
        Relations: {
            variants: {
                Shape: VariantDiscount[];
                Name: "VariantDiscount";
                Nullable: false;
            };
        };
    };
    VariantDiscount: {
        Name: "VariantDiscount";
        Shape: VariantDiscount;
        Include: Prisma.VariantDiscountInclude;
        Select: Prisma.VariantDiscountSelect;
        OrderBy: Prisma.VariantDiscountOrderByWithRelationInput;
        WhereUnique: Prisma.VariantDiscountWhereUniqueInput;
        Where: Prisma.VariantDiscountWhereInput;
        Create: {};
        Update: {};
        RelationName: "productVariant" | "discount";
        ListRelations: never;
        Relations: {
            productVariant: {
                Shape: ProductVariant;
                Name: "ProductVariant";
                Nullable: false;
            };
            discount: {
                Shape: Discount;
                Name: "Discount";
                Nullable: false;
            };
        };
    };
    Category: {
        Name: "Category";
        Shape: Category;
        Include: Prisma.CategoryInclude;
        Select: Prisma.CategorySelect;
        OrderBy: Prisma.CategoryOrderByWithRelationInput;
        WhereUnique: Prisma.CategoryWhereUniqueInput;
        Where: Prisma.CategoryWhereInput;
        Create: {};
        Update: {};
        RelationName: "products";
        ListRelations: "products";
        Relations: {
            products: {
                Shape: Product[];
                Name: "Product";
                Nullable: false;
            };
        };
    };
}