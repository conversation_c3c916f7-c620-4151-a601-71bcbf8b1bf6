import { createYoga } from 'graphql-yoga'
import { schema } from '../../../graphql/schema'
import { prisma } from '../../../lib/prisma'
import { NextRequest } from 'next/server'

// Create GraphQL Yoga instance
const yoga = createYoga({
  schema,
  context: () => ({
    prisma,
  }),
  // Enable GraphQL Playground in development
  graphiql: process.env.NODE_ENV === 'development',
  // CORS configuration
  cors: {
    origin: process.env.NODE_ENV === 'development'
      ? ['http://localhost:3000', 'http://localhost:3001']
      : process.env.ALLOWED_ORIGINS?.split(',') || [],
    credentials: true,
  },
  // Error handling
  maskedErrors: process.env.NODE_ENV === 'production',
  // Logging
  logging: process.env.NODE_ENV === 'development',
  fetchAPI: { Request, Response },
})

// Export handlers for Next.js App Router
export async function GET(request: NextRequest) {
  return yoga.handleRequest(request, {})
}

export async function POST(request: NextRequest) {
  return yoga.handleRequest(request, {})
}

export async function OPTIONS(request: NextRequest) {
  return yoga.handleRequest(request, {})
}