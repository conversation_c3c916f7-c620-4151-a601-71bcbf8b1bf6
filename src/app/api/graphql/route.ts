import { createYoga } from 'graphql-yoga'
import { schema } from '../../../graphql/schema'
import { prisma } from '../../../lib/prisma'

// Create GraphQL Yoga instance
const { handleRequest } = createYoga({
  schema,
  context: async (request) => ({
    prisma,
    request,
  }),
  // Enable GraphQL Playground in development
  graphiql: process.env.NODE_ENV === 'development',
  // CORS configuration
  cors: {
    origin: process.env.NODE_ENV === 'development'
      ? ['http://localhost:3000', 'http://localhost:3001']
      : process.env.ALLOWED_ORIGINS?.split(',') || [],
    credentials: true,
  },
  // Error handling
  maskedErrors: process.env.NODE_ENV === 'production',
  // Logging
  logging: process.env.NODE_ENV === 'development',
})

// Export handlers for Next.js App Router
export { handleRequest as GET, handleRequest as POST, handleRequest as OPTIONS }