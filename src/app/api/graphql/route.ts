import { NextRequest, NextResponse } from 'next/server';
import { graphql } from 'graphql';
import { schema } from '../../../graphql/schema';
import { prisma } from '../../../lib/prisma';

export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const query = url.searchParams.get('query');
  
  if (!query) {
    return NextResponse.json(
      { error: 'Query parameter is required' },
      { status: 400 }
    );
  }

  try {
    const result = await graphql({
      schema,
      source: query,
      contextValue: { prisma },
    });

    return NextResponse.json(result);
  } catch {
    return NextResponse.json(
      { error: 'GraphQL execution error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, variables, operationName } = body;

    const result = await graphql({
      schema,
      source: query,
      variableValues: variables,
      operationName,
      contextValue: { prisma },
    });

    return NextResponse.json(result);
  } catch {
    return NextResponse.json(
      { error: 'GraphQL execution error' },
      { status: 500 }
    );
  }
}
