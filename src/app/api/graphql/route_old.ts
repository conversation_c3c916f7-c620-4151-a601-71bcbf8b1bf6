import { createYoga } from 'graphql-yoga';
import { schema } from '../../../graphql/schema';
import { prisma } from '../../../lib/prisma';

const yoga = createYoga({
  schema,
  context: () => ({
    prisma,
  }),
  graphiql: process.env.NODE_ENV === 'development',
  fetchAPI: { Response },
});

export async function GET(request: Request) {
  return yoga.fetch(request);
}

export async function POST(request: Request) {
  return yoga.fetch(request);
}
