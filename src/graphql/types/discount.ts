import { builder } from '../../lib/builder'

// Discount Object Type
export const DiscountType = builder.prismaObject('Discount', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    description: t.exposeString('description', { nullable: true }),
    discountPercent: t.exposeFloat('discountPercent', { nullable: true }),
    discountAmount: t.exposeFloat('discountAmount', { nullable: true }),
    startDate: t.expose('startDate', { type: 'DateTime' }),
    endDate: t.expose('endDate', { type: 'DateTime' }),
    variants: t.relatedConnection('variants', {
      cursor: 'id',
      totalCount: true,
    }),
    // Virtual field for active status
    isActive: t.field({
      type: 'Boolean',
      resolve: (discount) => {
        const now = new Date()
        return discount.startDate <= now && discount.endDate >= now
      },
    }),
    // Virtual field for discount value
    discountValue: t.field({
      type: 'Float',
      resolve: (discount) => {
        return Number(discount.discountPercent || discount.discountAmount || 0)
      },
    }),
  }),
})

// VariantDiscount Object Type
export const VariantDiscountType = builder.prismaObject('VariantDiscount', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productVariantId: t.exposeString('productVariantId'),
    discountId: t.exposeString('discountId'),
    productVariant: t.relation('productVariant'),
    discount: t.relation('discount'),
  }),
})

// Discount Input Types
export const CreateDiscountInput = builder.inputType('CreateDiscountInput', {
  fields: (t) => ({
    name: t.string({ required: true }),
    description: t.string({ required: false }),
    discountPercent: t.float({ required: false }),
    discountAmount: t.float({ required: false }),
    startDate: t.field({ type: 'DateTime', required: true }),
    endDate: t.field({ type: 'DateTime', required: true }),
  }),
})

export const UpdateDiscountInput = builder.inputType('UpdateDiscountInput', {
  fields: (t) => ({
    id: t.string({ required: true }),
    name: t.string({ required: false }),
    description: t.string({ required: false }),
    discountPercent: t.float({ required: false }),
    discountAmount: t.float({ required: false }),
    startDate: t.field({ type: 'DateTime', required: false }),
    endDate: t.field({ type: 'DateTime', required: false }),
  }),
})

export const ApplyDiscountInput = builder.inputType('ApplyDiscountInput', {
  fields: (t) => ({
    discountId: t.string({ required: true }),
    productVariantIds: t.stringList({ required: true }),
  }),
})

export const DiscountFilterInput = builder.inputType('DiscountFilterInput', {
  fields: (t) => ({
    isActive: t.boolean({ required: false }),
    name: t.string({ required: false }),
  }),
})

// Discount Queries
builder.queryField('discount', (t) =>
  t.prismaField({
    type: 'Discount',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      builder.prisma.discount.findUnique({
        ...query,
        where: { id: args.id },
      }),
  })
)

builder.queryField('discounts', (t) =>
  t.prismaConnection({
    type: 'Discount',
    cursor: 'id',
    totalCount: true,
    args: {
      filter: t.arg({ type: DiscountFilterInput, required: false }),
    },
    resolve: (query, _parent, args) => {
      const where: any = {}
      
      if (args.filter) {
        if (args.filter.name) {
          where.name = {
            contains: args.filter.name,
            mode: 'insensitive',
          }
        }
        if (args.filter.isActive !== undefined) {
          const now = new Date()
          if (args.filter.isActive) {
            where.startDate = { lte: now }
            where.endDate = { gte: now }
          } else {
            where.OR = [
              { startDate: { gt: now } },
              { endDate: { lt: now } },
            ]
          }
        }
      }

      return builder.prisma.discount.findMany({
        ...query,
        where,
        orderBy: { startDate: 'desc' },
      })
    },
  })
)

// Discount Mutations
builder.mutationField('createDiscount', (t) =>
  t.prismaField({
    type: 'Discount',
    args: {
      input: t.arg({ type: CreateDiscountInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      // Validate that either discountPercent or discountAmount is provided
      if (!args.input.discountPercent && !args.input.discountAmount) {
        throw new Error('Either discountPercent or discountAmount must be provided')
      }

      // Validate date range
      if (args.input.startDate >= args.input.endDate) {
        throw new Error('Start date must be before end date')
      }

      return builder.prisma.discount.create({
        ...query,
        data: {
          name: args.input.name,
          description: args.input.description,
          discountPercent: args.input.discountPercent,
          discountAmount: args.input.discountAmount,
          startDate: args.input.startDate,
          endDate: args.input.endDate,
        },
      })
    },
  })
)

builder.mutationField('updateDiscount', (t) =>
  t.prismaField({
    type: 'Discount',
    args: {
      input: t.arg({ type: UpdateDiscountInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      const updateData: any = {}
      
      if (args.input.name) updateData.name = args.input.name
      if (args.input.description !== undefined) updateData.description = args.input.description
      if (args.input.discountPercent !== undefined) updateData.discountPercent = args.input.discountPercent
      if (args.input.discountAmount !== undefined) updateData.discountAmount = args.input.discountAmount
      if (args.input.startDate) updateData.startDate = args.input.startDate
      if (args.input.endDate) updateData.endDate = args.input.endDate

      // Validate date range if both dates are being updated
      if (updateData.startDate && updateData.endDate && updateData.startDate >= updateData.endDate) {
        throw new Error('Start date must be before end date')
      }

      return builder.prisma.discount.update({
        ...query,
        where: { id: args.input.id },
        data: updateData,
      })
    },
  })
)

builder.mutationField('applyDiscountToVariants', (t) =>
  t.field({
    type: 'Boolean',
    args: {
      input: t.arg({ type: ApplyDiscountInput, required: true }),
    },
    resolve: async (_parent, args) => {
      // Validate discount exists
      const discount = await builder.prisma.discount.findUnique({
        where: { id: args.input.discountId },
      })
      
      if (!discount) {
        throw new Error('Discount not found')
      }

      // Validate all product variants exist
      const variants = await builder.prisma.productVariant.findMany({
        where: {
          id: { in: args.input.productVariantIds },
        },
      })
      
      if (variants.length !== args.input.productVariantIds.length) {
        throw new Error('One or more product variants not found')
      }

      // Create variant discount relationships
      const variantDiscounts = args.input.productVariantIds.map(variantId => ({
        productVariantId: variantId,
        discountId: args.input.discountId,
      }))

      await builder.prisma.variantDiscount.createMany({
        data: variantDiscounts,
        skipDuplicates: true,
      })

      return true
    },
  })
)

builder.mutationField('removeDiscountFromVariants', (t) =>
  t.field({
    type: 'Boolean',
    args: {
      discountId: t.arg.string({ required: true }),
      productVariantIds: t.arg.stringList({ required: true }),
    },
    resolve: async (_parent, args) => {
      await builder.prisma.variantDiscount.deleteMany({
        where: {
          discountId: args.discountId,
          productVariantId: { in: args.productVariantIds },
        },
      })

      return true
    },
  })
)

builder.mutationField('deleteDiscount', (t) =>
  t.prismaField({
    type: 'Discount',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.discount.delete({
        ...query,
        where: { id: args.id },
      })
    },
  })
)
