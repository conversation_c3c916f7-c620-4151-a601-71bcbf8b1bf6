import { builder } from '../../lib/builder'

// ProductImage Object Type
export const ProductImageType = builder.prismaObject('ProductImage', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productId: t.exposeString('productId'),
    imageUrl: t.exposeString('imageUrl'),
    isPrimary: t.exposeBoolean('isPrimary'),
    product: t.relation('product'),
  }),
})

// ProductImage Input Types
export const CreateProductImageInput = builder.inputType('CreateProductImageInput', {
  fields: (t) => ({
    productId: t.string({ required: true }),
    imageUrl: t.string({ required: true }),
    isPrimary: t.boolean({ required: false }),
  }),
})

export const UpdateProductImageInput = builder.inputType('UpdateProductImageInput', {
  fields: (t) => ({
    id: t.string({ required: true }),
    imageUrl: t.string({ required: false }),
    isPrimary: t.boolean({ required: false }),
  }),
})

// ProductImage Queries
builder.queryField('productImage', (t) =>
  t.prismaField({
    type: 'ProductImage',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      builder.prisma.productImage.findUnique({
        ...query,
        where: { id: args.id },
      }),
  })
)

builder.queryField('productImages', (t) =>
  t.prismaConnection({
    type: 'ProductImage',
    cursor: 'id',
    totalCount: true,
    args: {
      productId: t.arg.string({ required: false }),
    },
    resolve: (query, _parent, args) => {
      const where = args.productId ? { productId: args.productId } : {}
      
      return builder.prisma.productImage.findMany({
        ...query,
        where,
        orderBy: [{ isPrimary: 'desc' }, { id: 'asc' }],
      })
    },
  })
)

// ProductImage Mutations
builder.mutationField('createProductImage', (t) =>
  t.prismaField({
    type: 'ProductImage',
    args: {
      input: t.arg({ type: CreateProductImageInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      // Validate product exists
      const product = await builder.prisma.product.findUnique({
        where: { id: args.input.productId },
      })
      
      if (!product) {
        throw new Error('Product not found')
      }

      // If setting as primary, unset other primary images for this product
      if (args.input.isPrimary) {
        await builder.prisma.productImage.updateMany({
          where: {
            productId: args.input.productId,
            isPrimary: true,
          },
          data: {
            isPrimary: false,
          },
        })
      }

      return builder.prisma.productImage.create({
        ...query,
        data: {
          productId: args.input.productId,
          imageUrl: args.input.imageUrl,
          isPrimary: args.input.isPrimary || false,
        },
      })
    },
  })
)

builder.mutationField('updateProductImage', (t) =>
  t.prismaField({
    type: 'ProductImage',
    args: {
      input: t.arg({ type: UpdateProductImageInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      const currentImage = await builder.prisma.productImage.findUnique({
        where: { id: args.input.id },
      })
      
      if (!currentImage) {
        throw new Error('Product image not found')
      }

      // If setting as primary, unset other primary images for this product
      if (args.input.isPrimary) {
        await builder.prisma.productImage.updateMany({
          where: {
            productId: currentImage.productId,
            isPrimary: true,
            id: { not: args.input.id },
          },
          data: {
            isPrimary: false,
          },
        })
      }

      const updateData: any = {}
      if (args.input.imageUrl) updateData.imageUrl = args.input.imageUrl
      if (args.input.isPrimary !== undefined) updateData.isPrimary = args.input.isPrimary

      return builder.prisma.productImage.update({
        ...query,
        where: { id: args.input.id },
        data: updateData,
      })
    },
  })
)

builder.mutationField('deleteProductImage', (t) =>
  t.prismaField({
    type: 'ProductImage',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.productImage.delete({
        ...query,
        where: { id: args.id },
      })
    },
  })
)
