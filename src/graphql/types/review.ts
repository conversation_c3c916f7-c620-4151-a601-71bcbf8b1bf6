/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { builder } from '../../lib/builder';
import { prisma } from '../../lib/prisma';
import { z } from 'zod';
import {
  PaginationInput,
  PageInfoInput,
  Pagination,
  
  SortInput,
  DateRangeInput,
  StringFilterInput,
  NumberRangeInput,
  calculateTraditionalPagination,
  calculateCursorPagination,
  createPageInfo,
  PaginationInputSchema,
  PageInfoInputSchema,
} from './pagination';

// Review object type
export const Review = builder.prismaObject('Review', {
  fields: (t) => ({
    id: t.exposeID('id'),
    customerId: t.exposeString('customerId'),
    productId: t.exposeString('productId'),
    rating: t.exposeInt('rating'),
    comment: t.exposeString('comment', { nullable: true }),
    images: t.exposeStringList('images'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    // Relations
    customer: t.relation('customer'),
    product: t.relation('product'),
    // Computed fields
    hasComment: t.boolean({
      resolve: (review) => !!review.comment,
    }),
    hasImages: t.boolean({
      resolve: (review) => review.images.length > 0,
    }),
    imageCount: t.int({
      resolve: (review) => review.images.length,
    }),
  }),
});

// Review filter input
const ReviewFilterSchema = z.object({
  id: z.string().optional(),
  customerId: z.string().optional(),
  customerIds: z.array(z.string()).optional(),
  productId: z.string().optional(),
  productIds: z.array(z.string()).optional(),
  rating: z.number().min(1).max(5).optional(),
  ratingMin: z.number().min(1).max(5).optional(),
  ratingMax: z.number().min(1).max(5).optional(),
  hasComment: z.boolean().optional(),
  hasImages: z.boolean().optional(),
  createdAt: z.object({
    from: z.date().optional(),
    to: z.date().optional(),
  }).optional(),
  comment: z.object({
    equals: z.string().optional(),
    contains: z.string().optional(),
    startsWith: z.string().optional(),
    endsWith: z.string().optional(),
  }).optional(),
  categoryId: z.string().optional(),
});

export const ReviewFilterInput = builder.inputType('ReviewFilterInput', {
  fields: (t) => ({
    id: t.string({ description: 'Filter by review ID' }),
    customerId: t.string({ description: 'Filter by customer ID' }),
    customerIds: t.stringList({ description: 'Filter by multiple customer IDs' }),
    productId: t.string({ description: 'Filter by product ID' }),
    productIds: t.stringList({ description: 'Filter by multiple product IDs' }),
    rating: t.int({ description: 'Filter by exact rating' }),
    ratingMin: t.int({ description: 'Filter by minimum rating' }),
    ratingMax: t.int({ description: 'Filter by maximum rating' }),
    hasComment: t.boolean({ description: 'Filter reviews with/without comments' }),
    hasImages: t.boolean({ description: 'Filter reviews with/without images' }),
    createdAt: t.field({ type: DateRangeInput, description: 'Filter by creation date range' }),
    comment: t.field({ type: StringFilterInput, description: 'Filter by comment content' }),
    categoryId: t.string({ description: 'Filter reviews for products in specific category' }),
  }),
});

// Review sort fields enum
export const ReviewSortField = builder.enumType('ReviewSortField', {
  values: {
    ID: { value: 'id' },
    RATING: { value: 'rating' },
    CREATED_AT: { value: 'createdAt' },
  },
});

// Review connection type for cursor-based pagination
export const ReviewConnection = builder.connectionObject({
  type: Review,
  name: 'ReviewConnection',
});

// Review list with traditional pagination
export const ReviewList = builder.objectType('ReviewList', {
  fields: (t) => ({
    items: t.field({ type: [Review], description: 'List of reviews' }),
    pagination: t.field({ type: Pagination, description: 'Pagination information' }),
  }),
});

// Review statistics
export const ReviewStats = builder.objectType('ReviewStats', {
  fields: (t) => ({
    totalReviews: t.int({ description: 'Total number of reviews' }),
    averageRating: t.float({ nullable: true, description: 'Average rating' }),
    ratingDistribution: t.field({
      type: [builder.objectType('RatingDistribution', {
        fields: (t) => ({
          rating: t.int(),
          count: t.int(),
          percentage: t.float(),
        }),
      })],
      description: 'Distribution of ratings',
    }),
    reviewsWithComments: t.int({ description: 'Number of reviews with comments' }),
    reviewsWithImages: t.int({ description: 'Number of reviews with images' }),
  }),
});

// Helper function to build Prisma where clause for reviews
function buildReviewWhereClause(filter: any) {
  const where: any = {};

  if (filter?.id) {
    where.id = filter.id;
  }

  if (filter?.customerId) {
    where.customerId = filter.customerId;
  }

  if (filter?.customerIds?.length) {
    where.customerId = { in: filter.customerIds };
  }

  if (filter?.productId) {
    where.productId = filter.productId;
  }

  if (filter?.productIds?.length) {
    where.productId = { in: filter.productIds };
  }

  if (filter?.rating) {
    where.rating = filter.rating;
  }

  if (filter?.ratingMin || filter?.ratingMax) {
    where.rating = {};
    if (filter.ratingMin) {
      where.rating.gte = filter.ratingMin;
    }
    if (filter.ratingMax) {
      where.rating.lte = filter.ratingMax;
    }
  }

  if (filter?.hasComment !== undefined) {
    if (filter.hasComment) {
      where.comment = { not: null };
    } else {
      where.comment = null;
    }
  }

  if (filter?.hasImages !== undefined) {
    if (filter.hasImages) {
      where.images = { isEmpty: false };
    } else {
      where.images = { isEmpty: true };
    }
  }

  if (filter?.createdAt) {
    where.createdAt = {};
    if (filter.createdAt.from) {
      where.createdAt.gte = filter.createdAt.from;
    }
    if (filter.createdAt.to) {
      where.createdAt.lte = filter.createdAt.to;
    }
  }

  if (filter?.comment) {
    where.comment = buildStringFilter(filter.comment);
  }

  if (filter?.categoryId) {
    where.product = {
      categoryId: filter.categoryId,
    };
  }

  return where;
}

function buildStringFilter(filter: any) {
  const result: any = {};

  if (filter.equals) result.equals = filter.equals;
  if (filter.contains) result.contains = filter.contains;
  if (filter.startsWith) result.startsWith = filter.startsWith;
  if (filter.endsWith) result.endsWith = filter.endsWith;

  if (filter.contains || filter.startsWith || filter.endsWith) {
    result.mode = 'insensitive';
  }

  return result;
}

function buildReviewOrderByClause(sort?: { field: string; direction: 'asc' | 'desc' }[]) {
  if (!sort?.length) {
    return [{ createdAt: 'desc' as const }];
  }

  return sort.map(s => ({ [s.field]: s.direction }));
}

// Query fields for reviews
builder.queryFields((t) => ({
  // Single review query
  review: t.prismaField({
    type: 'Review',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, root, args, ctx) => {
      return ctx.prisma.review.findUnique({
        ...query,
        where: { id: args.id },
      });
    },
  }),

  // Reviews with cursor-based pagination
  reviews: t.connection({
    type: Review,
    args: {
      filter: t.arg({ type: ReviewFilterInput }),
      sort: t.arg({ type: [SortInput] }),
    },
    resolve: async (root, args, ctx) => {
      const filter = args.filter;
      const sort = args.sort;
      
      const where = buildReviewWhereClause(filter);
      const orderBy = buildReviewOrderByClause(sort);

      // Get total count
      const totalCount = await ctx.prisma.review.count({ where });

      // Calculate pagination
      const { skip, take } = calculateCursorPagination(args, totalCount);

      // Get reviews
      const reviews = await ctx.prisma.review.findMany({
        where,
        orderBy,
        skip,
        take,
      });

      // Create page info
      const pageInfo = createPageInfo(reviews, totalCount, skip, take);

      return {
        edges: reviews.map((review, index) => ({
          node: review,
          cursor: pageInfo.startCursor || '',
        })),
        pageInfo,
      };
    },
  }),

  // Reviews with traditional pagination
  reviewsList: t.field({
    type: ReviewList,
    args: {
      pagination: t.arg({ type: PaginationInput }),
      filter: t.arg({ type: ReviewFilterInput }),
      sort: t.arg({ type: [SortInput] }),
    },
    validate: {
      schema: z.object({
        pagination: PaginationInputSchema.optional(),
        filter: ReviewFilterSchema.optional(),
        sort: z.array(z.object({
          field: z.string(),
          direction: z.enum(['asc', 'desc']),
        })).optional(),
      }),
    },
    resolve: async (root, args, ctx) => {
      const { pagination = {}, filter, sort } = args;
      
      const where = buildReviewWhereClause(filter);
      const orderBy = buildReviewOrderByClause(sort);

      // Get total count
      const totalCount = await ctx.prisma.review.count({ where });

      // Calculate pagination
      const { skip, take, pagination: paginationInfo } = calculateTraditionalPagination(
        pagination.page || 1,
        pagination.pageSize || 10,
        totalCount,
        pagination.offset,
        pagination.skip
      );

      // Get reviews
      const reviews = await ctx.prisma.review.findMany({
        where,
        orderBy,
        skip,
        take,
      });

      return {
        items: reviews,
        pagination: paginationInfo,
      };
    },
  }),

  // Review statistics
  reviewStats: t.field({
    type: ReviewStats,
    args: {
      filter: t.arg({ type: ReviewFilterInput }),
    },
    resolve: async (root, args, ctx) => {
      const where = buildReviewWhereClause(args.filter);

      // Get total count
      const totalReviews = await ctx.prisma.review.count({ where });

      // Get average rating
      const avgResult = await ctx.prisma.review.aggregate({
        where,
        _avg: { rating: true },
      });

      // Get rating distribution
      const ratingCounts = await ctx.prisma.review.groupBy({
        by: ['rating'],
        where,
        _count: { rating: true },
      });

      const ratingDistribution = [1, 2, 3, 4, 5].map(rating => {
        const found = ratingCounts.find(r => r.rating === rating);
        const count = found?._count.rating || 0;
        return {
          rating,
          count,
          percentage: totalReviews > 0 ? (count / totalReviews) * 100 : 0,
        };
      });

      // Get reviews with comments and images
      const reviewsWithComments = await ctx.prisma.review.count({
        where: {
          ...where,
          comment: { not: null },
        },
      });

      const reviewsWithImages = await ctx.prisma.review.count({
        where: {
          ...where,
          images: { isEmpty: false },
        },
      });

      return {
        totalReviews,
        averageRating: avgResult._avg.rating,
        ratingDistribution,
        reviewsWithComments,
        reviewsWithImages,
      };
    },
  }),
}));
