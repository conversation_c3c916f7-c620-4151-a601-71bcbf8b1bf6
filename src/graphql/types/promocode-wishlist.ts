import { builder } from '../../lib/builder';
import { prisma } from '../../lib/prisma';
import { z } from 'zod';
import {
  PaginationInput,
  PageInfoInput,
  Pagination,
  PageInfo,
  SortInput,
  DateRangeInput,
  StringFilterInput,
  NumberRangeInput,
  calculateTraditionalPagination,
  calculateCursorPagination,
  createPageInfo,
  PaginationInputSchema,
  PageInfoInputSchema,
} from './pagination';

// PromoCode object type
export const PromoCode = builder.prismaObject('PromoCode', {
  fields: (t) => ({
    id: t.exposeID('id'),
    code: t.exposeString('code'),
    discountType: t.exposeString('discountType'),
    discountValue: t.exposeFloat('discountValue'),
    expiration: t.expose('expiration', { type: 'DateTime' }),
    usageLimit: t.exposeInt('usageLimit', { nullable: true }),
    usedCount: t.exposeInt('usedCount'),
    isActive: t.exposeBoolean('isActive'),
    // Relations
    usages: t.relation('usages'),
    // Computed fields
    isExpired: t.boolean({
      resolve: (promoCode) => new Date() > promoCode.expiration,
    }),
    remainingUses: t.int({
      nullable: true,
      resolve: (promoCode) => {
        if (!promoCode.usageLimit) return null;
        return Math.max(0, promoCode.usageLimit - promoCode.usedCount);
      },
    }),
    isValid: t.boolean({
      resolve: (promoCode) => {
        const now = new Date();
        const notExpired = now <= promoCode.expiration;
        const hasUsesLeft = !promoCode.usageLimit || promoCode.usedCount < promoCode.usageLimit;
        return promoCode.isActive && notExpired && hasUsesLeft;
      },
    }),
  }),
});

// PromoCodeUsage object type
export const PromoCodeUsage = builder.prismaObject('PromoCodeUsage', {
  fields: (t) => ({
    id: t.exposeID('id'),
    promoCodeId: t.exposeString('promoCodeId'),
    customerId: t.exposeString('customerId'),
    usedAt: t.expose('usedAt', { type: 'DateTime' }),
    // Relations
    promoCode: t.relation('promoCode'),
    customer: t.relation('customer'),
  }),
});

// Wishlist object type
export const Wishlist = builder.prismaObject('Wishlist', {
  fields: (t) => ({
    id: t.exposeID('id'),
    customerId: t.exposeString('customerId'),
    // Relations
    customer: t.relation('customer'),
    items: t.relation('items'),
    // Computed fields
    itemCount: t.int({
      resolve: async (wishlist, args, ctx) => {
        return ctx.prisma.wishlistItem.count({
          where: { wishlistId: wishlist.id },
        });
      },
    }),
    totalValue: t.float({
      resolve: async (wishlist, args, ctx) => {
        const items = await ctx.prisma.wishlistItem.findMany({
          where: { wishlistId: wishlist.id },
          include: { productVariant: true },
        });
        return items.reduce((sum, item) => sum + item.productVariant.price, 0);
      },
    }),
  }),
});

// WishlistItem object type
export const WishlistItem = builder.prismaObject('WishlistItem', {
  fields: (t) => ({
    id: t.exposeID('id'),
    wishlistId: t.exposeString('wishlistId'),
    productVariantId: t.exposeString('productVariantId'),
    addedAt: t.expose('addedAt', { type: 'DateTime' }),
    // Relations
    wishlist: t.relation('wishlist'),
    productVariant: t.relation('productVariant'),
  }),
});

// PromoCode filter input
const PromoCodeFilterSchema = z.object({
  id: z.string().optional(),
  code: z.object({
    equals: z.string().optional(),
    contains: z.string().optional(),
    startsWith: z.string().optional(),
    endsWith: z.string().optional(),
  }).optional(),
  discountType: z.string().optional(),
  discountValue: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
  }).optional(),
  isActive: z.boolean().optional(),
  isExpired: z.boolean().optional(),
  isValid: z.boolean().optional(),
  expiration: z.object({
    from: z.date().optional(),
    to: z.date().optional(),
  }).optional(),
  usageLimit: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
  }).optional(),
  usedCount: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
  }).optional(),
});

export const PromoCodeFilterInput = builder.inputType('PromoCodeFilterInput', {
  fields: (t) => ({
    id: t.string({ description: 'Filter by promo code ID' }),
    code: t.field({ type: StringFilterInput, description: 'Filter by promo code' }),
    discountType: t.string({ description: 'Filter by discount type (fixed/percent)' }),
    discountValue: t.field({ type: NumberRangeInput, description: 'Filter by discount value range' }),
    isActive: t.boolean({ description: 'Filter by active status' }),
    isExpired: t.boolean({ description: 'Filter by expiration status' }),
    isValid: t.boolean({ description: 'Filter by overall validity' }),
    expiration: t.field({ type: DateRangeInput, description: 'Filter by expiration date range' }),
    usageLimit: t.field({ type: NumberRangeInput, description: 'Filter by usage limit range' }),
    usedCount: t.field({ type: NumberRangeInput, description: 'Filter by used count range' }),
  }),
});

// Wishlist filter input
const WishlistFilterSchema = z.object({
  id: z.string().optional(),
  customerId: z.string().optional(),
  customerIds: z.array(z.string()).optional(),
  hasItems: z.boolean().optional(),
  productId: z.string().optional(),
  categoryId: z.string().optional(),
  priceRange: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
  }).optional(),
});

export const WishlistFilterInput = builder.inputType('WishlistFilterInput', {
  fields: (t) => ({
    id: t.string({ description: 'Filter by wishlist ID' }),
    customerId: t.string({ description: 'Filter by customer ID' }),
    customerIds: t.stringList({ description: 'Filter by multiple customer IDs' }),
    hasItems: t.boolean({ description: 'Filter wishlists with/without items' }),
    productId: t.string({ description: 'Filter wishlists containing specific product' }),
    categoryId: t.string({ description: 'Filter wishlists containing products from category' }),
    priceRange: t.field({ type: NumberRangeInput, description: 'Filter by total value range' }),
  }),
});

// PromoCode sort fields enum
export const PromoCodeSortField = builder.enumType('PromoCodeSortField', {
  values: {
    ID: { value: 'id' },
    CODE: { value: 'code' },
    DISCOUNT_VALUE: { value: 'discountValue' },
    EXPIRATION: { value: 'expiration' },
    USED_COUNT: { value: 'usedCount' },
  },
});

// PromoCode connection and list types
export const PromoCodeConnection = builder.connectionObject({
  type: PromoCode,
  name: 'PromoCodeConnection',
});

export const PromoCodeList = builder.objectType('PromoCodeList', {
  fields: (t) => ({
    items: t.field({ type: [PromoCode], description: 'List of promo codes' }),
    pagination: t.field({ type: Pagination, description: 'Pagination information' }),
  }),
});

// Wishlist connection and list types
export const WishlistConnection = builder.connectionObject({
  type: Wishlist,
  name: 'WishlistConnection',
});

export const WishlistList = builder.objectType('WishlistList', {
  fields: (t) => ({
    items: t.field({ type: [Wishlist], description: 'List of wishlists' }),
    pagination: t.field({ type: Pagination, description: 'Pagination information' }),
  }),
});

// Helper functions
function buildPromoCodeWhereClause(filter: any) {
  const where: any = {};

  if (filter?.id) {
    where.id = filter.id;
  }

  if (filter?.code) {
    where.code = buildStringFilter(filter.code);
  }

  if (filter?.discountType) {
    where.discountType = filter.discountType;
  }

  if (filter?.discountValue) {
    where.discountValue = {};
    if (filter.discountValue.min) {
      where.discountValue.gte = filter.discountValue.min;
    }
    if (filter.discountValue.max) {
      where.discountValue.lte = filter.discountValue.max;
    }
  }

  if (filter?.isActive !== undefined) {
    where.isActive = filter.isActive;
  }

  if (filter?.expiration) {
    where.expiration = {};
    if (filter.expiration.from) {
      where.expiration.gte = filter.expiration.from;
    }
    if (filter.expiration.to) {
      where.expiration.lte = filter.expiration.to;
    }
  }

  if (filter?.isExpired !== undefined) {
    const now = new Date();
    if (filter.isExpired) {
      where.expiration = { lt: now };
    } else {
      where.expiration = { gte: now };
    }
  }

  if (filter?.usageLimit) {
    where.usageLimit = {};
    if (filter.usageLimit.min) {
      where.usageLimit.gte = filter.usageLimit.min;
    }
    if (filter.usageLimit.max) {
      where.usageLimit.lte = filter.usageLimit.max;
    }
  }

  if (filter?.usedCount) {
    where.usedCount = {};
    if (filter.usedCount.min) {
      where.usedCount.gte = filter.usedCount.min;
    }
    if (filter.usedCount.max) {
      where.usedCount.lte = filter.usedCount.max;
    }
  }

  return where;
}

function buildWishlistWhereClause(filter: any) {
  const where: any = {};

  if (filter?.id) {
    where.id = filter.id;
  }

  if (filter?.customerId) {
    where.customerId = filter.customerId;
  }

  if (filter?.customerIds?.length) {
    where.customerId = { in: filter.customerIds };
  }

  if (filter?.hasItems !== undefined) {
    if (filter.hasItems) {
      where.items = { some: {} };
    } else {
      where.items = { none: {} };
    }
  }

  if (filter?.productId) {
    where.items = {
      some: {
        productVariant: {
          productId: filter.productId,
        },
      },
    };
  }

  if (filter?.categoryId) {
    where.items = {
      some: {
        productVariant: {
          product: {
            categoryId: filter.categoryId,
          },
        },
      },
    };
  }

  return where;
}

function buildStringFilter(filter: any) {
  const result: any = {};

  if (filter.equals) result.equals = filter.equals;
  if (filter.contains) result.contains = filter.contains;
  if (filter.startsWith) result.startsWith = filter.startsWith;
  if (filter.endsWith) result.endsWith = filter.endsWith;

  if (filter.contains || filter.startsWith || filter.endsWith) {
    result.mode = 'insensitive';
  }

  return result;
}

function buildOrderByClause(sort?: { field: string; direction: 'asc' | 'desc' }[], defaultField = 'id') {
  if (!sort?.length) {
    return [{ [defaultField]: 'desc' as const }];
  }

  return sort.map(s => ({ [s.field]: s.direction }));
}

// Query fields
builder.queryFields((t) => ({
  // PromoCode queries
  promoCode: t.prismaField({
    type: 'PromoCode',
    nullable: true,
    args: {
      id: t.arg.string(),
      code: t.arg.string(),
    },
    resolve: async (query, root, args, ctx) => {
      const where: any = {};
      if (args.id) where.id = args.id;
      if (args.code) where.code = args.code;

      return ctx.prisma.promoCode.findFirst({
        ...query,
        where,
      });
    },
  }),

  promoCodes: t.connection({
    type: PromoCode,
    args: {
      filter: t.arg({ type: PromoCodeFilterInput }),
      sort: t.arg({ type: [SortInput] }),
    },
    resolve: async (root, args, ctx) => {
      const where = buildPromoCodeWhereClause(args.filter);
      const orderBy = buildOrderByClause(args.sort, 'expiration');

      const totalCount = await ctx.prisma.promoCode.count({ where });
      const { skip, take } = calculateCursorPagination(args, totalCount);

      const promoCodes = await ctx.prisma.promoCode.findMany({
        where,
        orderBy,
        skip,
        take,
      });

      const pageInfo = createPageInfo(promoCodes, totalCount, skip, take);

      return {
        edges: promoCodes.map((promoCode) => ({
          node: promoCode,
          cursor: pageInfo.startCursor || '',
        })),
        pageInfo,
      };
    },
  }),

  promoCodesList: t.field({
    type: PromoCodeList,
    args: {
      pagination: t.arg({ type: PaginationInput }),
      filter: t.arg({ type: PromoCodeFilterInput }),
      sort: t.arg({ type: [SortInput] }),
    },
    validate: {
      schema: z.object({
        pagination: PaginationInputSchema.optional(),
        filter: PromoCodeFilterSchema.optional(),
        sort: z.array(z.object({
          field: z.string(),
          direction: z.enum(['asc', 'desc']),
        })).optional(),
      }),
    },
    resolve: async (root, args, ctx) => {
      const { pagination = {}, filter, sort } = args;
      
      const where = buildPromoCodeWhereClause(filter);
      const orderBy = buildOrderByClause(sort, 'expiration');

      const totalCount = await ctx.prisma.promoCode.count({ where });
      const { skip, take, pagination: paginationInfo } = calculateTraditionalPagination(
        pagination.page || 1,
        pagination.pageSize || 10,
        totalCount,
        pagination.offset,
        pagination.skip
      );

      const promoCodes = await ctx.prisma.promoCode.findMany({
        where,
        orderBy,
        skip,
        take,
      });

      return {
        items: promoCodes,
        pagination: paginationInfo,
      };
    },
  }),

  // Wishlist queries
  wishlist: t.prismaField({
    type: 'Wishlist',
    nullable: true,
    args: {
      id: t.arg.string(),
      customerId: t.arg.string(),
    },
    resolve: async (query, root, args, ctx) => {
      const where: any = {};
      if (args.id) where.id = args.id;
      if (args.customerId) where.customerId = args.customerId;

      return ctx.prisma.wishlist.findFirst({
        ...query,
        where,
      });
    },
  }),

  wishlists: t.connection({
    type: Wishlist,
    args: {
      filter: t.arg({ type: WishlistFilterInput }),
      sort: t.arg({ type: [SortInput] }),
    },
    resolve: async (root, args, ctx) => {
      const where = buildWishlistWhereClause(args.filter);
      const orderBy = buildOrderByClause(args.sort, 'id');

      const totalCount = await ctx.prisma.wishlist.count({ where });
      const { skip, take } = calculateCursorPagination(args, totalCount);

      const wishlists = await ctx.prisma.wishlist.findMany({
        where,
        orderBy,
        skip,
        take,
      });

      const pageInfo = createPageInfo(wishlists, totalCount, skip, take);

      return {
        edges: wishlists.map((wishlist) => ({
          node: wishlist,
          cursor: pageInfo.startCursor || '',
        })),
        pageInfo,
      };
    },
  }),

  wishlistsList: t.field({
    type: WishlistList,
    args: {
      pagination: t.arg({ type: PaginationInput }),
      filter: t.arg({ type: WishlistFilterInput }),
      sort: t.arg({ type: [SortInput] }),
    },
    validate: {
      schema: z.object({
        pagination: PaginationInputSchema.optional(),
        filter: WishlistFilterSchema.optional(),
        sort: z.array(z.object({
          field: z.string(),
          direction: z.enum(['asc', 'desc']),
        })).optional(),
      }),
    },
    resolve: async (root, args, ctx) => {
      const { pagination = {}, filter, sort } = args;
      
      const where = buildWishlistWhereClause(filter);
      const orderBy = buildOrderByClause(sort, 'id');

      const totalCount = await ctx.prisma.wishlist.count({ where });
      const { skip, take, pagination: paginationInfo } = calculateTraditionalPagination(
        pagination.page || 1,
        pagination.pageSize || 10,
        totalCount,
        pagination.offset,
        pagination.skip
      );

      const wishlists = await ctx.prisma.wishlist.findMany({
        where,
        orderBy,
        skip,
        take,
      });

      return {
        items: wishlists,
        pagination: paginationInfo,
      };
    },
  }),
}));
