import { builder } from '../../lib/builder'

// Size Object Type
export const SizeType = builder.prismaObject('Size', {
  fields: (t) => ({
    id: t.exposeID('id'),
    sizeLabel: t.exposeString('sizeLabel'),
    variants: t.relatedConnection('variants', {
      cursor: 'id',
      totalCount: true,
    }),
  }),
})

// Size Input Types
export const CreateSizeInput = builder.inputType('CreateSizeInput', {
  fields: (t) => ({
    sizeLabel: t.string({ required: true }),
  }),
})

export const UpdateSizeInput = builder.inputType('UpdateSizeInput', {
  fields: (t) => ({
    id: t.string({ required: true }),
    sizeLabel: t.string({ required: false }),
  }),
})

// Size Queries
builder.queryField('size', (t) =>
  t.prismaField({
    type: 'Size',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      builder.prisma.size.findUnique({
        ...query,
        where: { id: args.id },
      }),
  })
)

builder.queryField('sizes', (t) =>
  t.prismaConnection({
    type: 'Size',
    cursor: 'id',
    totalCount: true,
    resolve: (query) =>
      builder.prisma.size.findMany({
        ...query,
        orderBy: { sizeLabel: 'asc' },
      }),
  })
)

// Size Mutations
builder.mutationField('createSize', (t) =>
  t.prismaField({
    type: 'Size',
    args: {
      input: t.arg({ type: CreateSizeInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.size.create({
        ...query,
        data: {
          sizeLabel: args.input.sizeLabel,
        },
      })
    },
  })
)

builder.mutationField('updateSize', (t) =>
  t.prismaField({
    type: 'Size',
    args: {
      input: t.arg({ type: UpdateSizeInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      const updateData: any = {}
      if (args.input.sizeLabel) updateData.sizeLabel = args.input.sizeLabel

      return builder.prisma.size.update({
        ...query,
        where: { id: args.input.id },
        data: updateData,
      })
    },
  })
)

builder.mutationField('deleteSize', (t) =>
  t.prismaField({
    type: 'Size',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.size.delete({
        ...query,
        where: { id: args.id },
      })
    },
  })
)
