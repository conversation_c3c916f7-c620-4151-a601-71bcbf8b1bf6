/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { builder } from '../../lib/builder';
import { prisma } from '../../lib/prisma';
import { z } from 'zod';

// Search result union type
export const SearchResult = builder.unionType('SearchResult', {
  types: ['Product', 'Customer', 'Order'],
  resolveType: (obj) => {
    if ('name' in obj && 'description' in obj) return 'Product';
    if ('firstName' in obj && 'lastName' in obj) return 'Customer';
    if ('orderDate' in obj && 'totalAmount' in obj) return 'Order';
    throw new Error('Unknown search result type');
  },
});

// Global search input
export const GlobalSearchInput = builder.inputType('GlobalSearchInput', {
  fields: (t) => ({
    query: t.string({ required: true, description: 'Search query string' }),
    types: t.stringList({ 
      description: 'Types to search in: product, customer, order',
      defaultValue: ['product', 'customer', 'order']
    }),
    limit: t.int({ defaultValue: 10, description: 'Maximum results per type' }),
  }),
});

// Search results type
export const SearchResults = builder.objectType('SearchResults', {
  fields: (t) => ({
    products: t.field({ 
      type: ['Product'], 
      description: 'Product search results' 
    }),
    customers: t.field({ 
      type: ['Customer'], 
      description: 'Customer search results' 
    }),
    orders: t.field({ 
      type: ['Order'], 
      description: 'Order search results' 
    }),
    totalResults: t.int({ description: 'Total number of results across all types' }),
  }),
});

// Analytics types
export const SalesAnalytics = builder.objectType('SalesAnalytics', {
  fields: (t) => ({
    totalRevenue: t.float({ description: 'Total revenue' }),
    totalOrders: t.int({ description: 'Total number of orders' }),
    averageOrderValue: t.float({ description: 'Average order value' }),
    topSellingProducts: t.field({
      type: [builder.objectType('TopSellingProduct', {
        fields: (t) => ({
          product: t.field({ type: 'Product' }),
          totalSold: t.int(),
          revenue: t.float(),
        }),
      })],
      description: 'Top selling products',
    }),
    salesByCategory: t.field({
      type: [builder.objectType('CategorySales', {
        fields: (t) => ({
          category: t.field({ type: 'Category' }),
          totalSold: t.int(),
          revenue: t.float(),
        }),
      })],
      description: 'Sales breakdown by category',
    }),
    salesByMonth: t.field({
      type: [builder.objectType('MonthlySales', {
        fields: (t) => ({
          month: t.string(),
          year: t.int(),
          totalOrders: t.int(),
          revenue: t.float(),
        }),
      })],
      description: 'Monthly sales data',
    }),
  }),
});

// Date range input for analytics
export const AnalyticsDateRangeInput = builder.inputType('AnalyticsDateRangeInput', {
  fields: (t) => ({
    from: t.field({ type: 'DateTime', required: true }),
    to: t.field({ type: 'DateTime', required: true }),
  }),
});

// Inventory status type
export const InventoryStatus = builder.objectType('InventoryStatus', {
  fields: (t) => ({
    totalProducts: t.int(),
    totalVariants: t.int(),
    inStockVariants: t.int(),
    outOfStockVariants: t.int(),
    lowStockVariants: t.field({
      type: [builder.objectType('LowStockVariant', {
        fields: (t) => ({
          variant: t.field({ type: 'ProductVariant' }),
          quantity: t.int(),
        }),
      })],
      description: 'Variants with low stock (< 10 items)',
    }),
  }),
});

// Add utility queries
builder.queryFields((t) => ({
  // Global search across all entities
  globalSearch: t.field({
    type: SearchResults,
    args: {
      input: t.arg({ type: GlobalSearchInput, required: true }),
    },
    validate: {
      schema: z.object({
        input: z.object({
          query: z.string().min(1),
          types: z.array(z.enum(['product', 'customer', 'order'])).optional(),
          limit: z.number().int().positive().max(50).optional(),
        }),
      }),
    },
    resolve: async (root, args, ctx) => {
      const { query, types = ['product', 'customer', 'order'], limit = 10 } = args.input;
      
      const results: any = {
        products: [],
        customers: [],
        orders: [],
        totalResults: 0,
      };

      // Search products
      if (types.includes('product')) {
        const products = await ctx.prisma.product.findMany({
          where: {
            OR: [
              { name: { contains: query, mode: 'insensitive' } },
              { description: { contains: query, mode: 'insensitive' } },
            ],
          },
          take: limit,
        });
        results.products = products;
        results.totalResults += products.length;
      }

      // Search customers
      if (types.includes('customer')) {
        const customers = await ctx.prisma.customer.findMany({
          where: {
            OR: [
              { firstName: { contains: query, mode: 'insensitive' } },
              { lastName: { contains: query, mode: 'insensitive' } },
              { email: { contains: query, mode: 'insensitive' } },
            ],
          },
          take: limit,
        });
        results.customers = customers;
        results.totalResults += customers.length;
      }

      // Search orders (by customer name or order ID)
      if (types.includes('order')) {
        const orders = await ctx.prisma.order.findMany({
          where: {
            OR: [
              { id: { contains: query, mode: 'insensitive' } },
              {
                customer: {
                  OR: [
                    { firstName: { contains: query, mode: 'insensitive' } },
                    { lastName: { contains: query, mode: 'insensitive' } },
                    { email: { contains: query, mode: 'insensitive' } },
                  ],
                },
              },
            ],
          },
          take: limit,
        });
        results.orders = orders;
        results.totalResults += orders.length;
      }

      return results;
    },
  }),

  // Sales analytics
  salesAnalytics: t.field({
    type: SalesAnalytics,
    args: {
      dateRange: t.arg({ type: AnalyticsDateRangeInput }),
    },
    resolve: async (root, args, ctx) => {
      const { dateRange } = args;
      
      const where: any = {};
      if (dateRange) {
        where.orderDate = {
          gte: dateRange.from,
          lte: dateRange.to,
        };
      }

      // Get basic stats
      const orderStats = await ctx.prisma.order.aggregate({
        where: { ...where, status: { not: 'cancelled' } },
        _sum: { totalAmount: true },
        _count: true,
        _avg: { totalAmount: true },
      });

      // Get top selling products
      const topSellingProducts = await ctx.prisma.orderItem.groupBy({
        by: ['productVariantId'],
        where: {
          order: { ...where, status: { not: 'cancelled' } },
        },
        _sum: { quantity: true, priceAtPurchase: true },
        orderBy: { _sum: { quantity: 'desc' } },
        take: 10,
      });

      const topProducts = await Promise.all(
        topSellingProducts.map(async (item) => {
          const variant = await ctx.prisma.productVariant.findUnique({
            where: { id: item.productVariantId },
            include: { product: true },
          });
          return {
            product: variant?.product,
            totalSold: item._sum.quantity || 0,
            revenue: item._sum.priceAtPurchase || 0,
          };
        })
      );

      // Get sales by category
      const categoryStats = await ctx.prisma.orderItem.groupBy({
        by: ['productVariantId'],
        where: {
          order: { ...where, status: { not: 'cancelled' } },
        },
        _sum: { quantity: true, priceAtPurchase: true },
      });

      // This would need more complex aggregation for categories
      // For now, returning empty array
      const salesByCategory: any[] = [];

      // Get monthly sales
      const monthlySales = await ctx.prisma.order.groupBy({
        by: ['orderDate'],
        where: { ...where, status: { not: 'cancelled' } },
        _sum: { totalAmount: true },
        _count: true,
      });

      // Process monthly data (simplified)
      const salesByMonth = monthlySales.map((sale) => ({
        month: sale.orderDate.toISOString().slice(0, 7), // YYYY-MM format
        year: sale.orderDate.getFullYear(),
        totalOrders: sale._count,
        revenue: sale._sum.totalAmount || 0,
      }));

      return {
        totalRevenue: orderStats._sum.totalAmount || 0,
        totalOrders: orderStats._count,
        averageOrderValue: orderStats._avg.totalAmount || 0,
        topSellingProducts: topProducts.filter(p => p.product),
        salesByCategory,
        salesByMonth,
      };
    },
  }),

  // Inventory status
  inventoryStatus: t.field({
    type: InventoryStatus,
    resolve: async (root, args, ctx) => {
      const totalProducts = await ctx.prisma.product.count();
      const totalVariants = await ctx.prisma.productVariant.count();
      const inStockVariants = await ctx.prisma.productVariant.count({
        where: { quantity: { gt: 0 } },
      });
      const outOfStockVariants = await ctx.prisma.productVariant.count({
        where: { quantity: { lte: 0 } },
      });

      const lowStockVariants = await ctx.prisma.productVariant.findMany({
        where: { quantity: { lt: 10, gt: 0 } },
        take: 20,
      });

      return {
        totalProducts,
        totalVariants,
        inStockVariants,
        outOfStockVariants,
        lowStockVariants: lowStockVariants.map(variant => ({
          variant,
          quantity: variant.quantity,
        })),
      };
    },
  }),

  // Health check
  health: t.field({
    type: builder.objectType('HealthCheck', {
      fields: (t) => ({
        status: t.string(),
        timestamp: t.field({ type: 'DateTime' }),
        uptime: t.float(),
        database: t.string(),
      }),
    }),
    resolve: async (root, args, ctx) => {
      try {
        // Test database connection
        await ctx.prisma.$queryRaw`SELECT 1`;
        
        return {
          status: 'healthy',
          timestamp: new Date(),
          uptime: process.uptime(),
          database: 'connected',
        };
      } catch (error) {
        return {
          status: 'unhealthy',
          timestamp: new Date(),
          uptime: process.uptime(),
          database: 'disconnected',
        };
      }
    },
  }),
}));
