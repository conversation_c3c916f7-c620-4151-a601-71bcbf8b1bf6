import { builder } from '../../lib/builder'

// Category Object Type
export const CategoryType = builder.prismaObject('Category', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    products: t.relatedConnection('products', {
      cursor: 'id',
      totalCount: true,
    }),
  }),
})

// Category Input Types
export const CreateCategoryInput = builder.inputType('CreateCategoryInput', {
  fields: (t) => ({
    name: t.string({ required: true }),
  }),
})

export const UpdateCategoryInput = builder.inputType('UpdateCategoryInput', {
  fields: (t) => ({
    id: t.string({ required: true }),
    name: t.string({ required: false }),
  }),
})

// Category Queries
builder.queryField('category', (t) =>
  t.prismaField({
    type: 'Category',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      builder.prisma.category.findUnique({
        ...query,
        where: { id: args.id },
      }),
  })
)

builder.queryField('categories', (t) =>
  t.prismaConnection({
    type: 'Category',
    cursor: 'id',
    totalCount: true,
    resolve: (query) =>
      builder.prisma.category.findMany({
        ...query,
        orderBy: { name: 'asc' },
      }),
  })
)

// Category Mutations
builder.mutationField('createCategory', (t) =>
  t.prismaField({
    type: 'Category',
    args: {
      input: t.arg({ type: CreateCategoryInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.category.create({
        ...query,
        data: {
          name: args.input.name,
        },
      })
    },
  })
)

builder.mutationField('updateCategory', (t) =>
  t.prismaField({
    type: 'Category',
    args: {
      input: t.arg({ type: UpdateCategoryInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.category.update({
        ...query,
        where: { id: args.input.id },
        data: {
          ...(args.input.name && { name: args.input.name }),
        },
      })
    },
  })
)

builder.mutationField('deleteCategory', (t) =>
  t.prismaField({
    type: 'Category',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.category.delete({
        ...query,
        where: { id: args.id },
      })
    },
  })
)
