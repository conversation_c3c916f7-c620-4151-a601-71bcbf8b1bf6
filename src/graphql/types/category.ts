import { builder } from '../../lib/builder'
import { createConnection } from '../../lib/pagination'

// Category GraphQL type
export const Category = builder.prismaObject('Category', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    description: t.exposeString('description', { nullable: true }),
    slug: t.exposeString('slug'),
    isActive: t.exposeBoolean('isActive'),
    sortOrder: t.exposeInt('sortOrder'),
    parentId: t.exposeString('parentId', { nullable: true }),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),

    // Relations
    parent: t.relation('parent', { nullable: true }),
    children: t.relation('children'),
    products: t.relation('products'),

    // Computed fields
    productCount: t.int({
      resolve: async (category, args, { prisma }) => {
        return prisma.product.count({
          where: { categoryId: category.id }
        })
      }
    }),

    isParent: t.boolean({
      resolve: async (category, args, { prisma }) => {
        const childCount = await prisma.category.count({
          where: { parentId: category.id }
        })
        return childCount > 0
      }
    }),
  }),
})

// Simple filter inputs
export const CategoryWhereInput = builder.inputType('CategoryWhereInput', {
  fields: (t) => ({
    isActive: t.boolean({ required: false }),
    parentId: t.string({ required: false }),
  }),
})

export const CategoryOrderByInput = builder.inputType('CategoryOrderByInput', {
  fields: (t) => ({
    name: t.string({ required: false }),
    sortOrder: t.string({ required: false }),
    createdAt: t.string({ required: false }),
  }),
})

// Create connection types for cursor-based pagination
export const { Connection: CategoryConnection, Edge: CategoryEdge } = createConnection('Category', Category)

// Category create input
export const CategoryCreateInput = builder.inputType('CategoryCreateInput', {
  fields: (t) => ({
    name: t.string({ required: true }),
    description: t.string({ required: false }),
    slug: t.string({ required: true }),
    isActive: t.boolean({ required: false }),
    sortOrder: t.int({ required: false }),
    parentId: t.string({ required: false }),
  }),
})

// Category update input
export const CategoryUpdateInput = builder.inputType('CategoryUpdateInput', {
  fields: (t) => ({
    name: t.string({ required: false }),
    description: t.string({ required: false }),
    slug: t.string({ required: false }),
    isActive: t.boolean({ required: false }),
    sortOrder: t.int({ required: false }),
    parentId: t.string({ required: false }),
  }),
})