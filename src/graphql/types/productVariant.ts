import { builder } from '../../lib/builder'
import { createConnection } from '../../lib/pagination'

// ProductVariant GraphQL type
export const ProductVariant = builder.prismaObject('ProductVariant', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productId: t.exposeString('productId'),
    sizeId: t.exposeString('sizeId'),
    colorId: t.exposeString('colorId'),
    sku: t.exposeString('sku'),
    price: t.exposeFloat('price'),
    compareAtPrice: t.exposeFloat('compareAtPrice', { nullable: true }),
    costPrice: t.exposeFloat('costPrice', { nullable: true }),
    quantity: t.exposeInt('quantity'),
    lowStockThreshold: t.exposeInt('lowStockThreshold'),
    isActive: t.exposeBoolean('isActive'),
    weight: t.exposeFloat('weight', { nullable: true }),
    barcode: t.exposeString('barcode', { nullable: true }),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),

    // Relations
    product: t.relation('product'),
    size: t.relation('size'),
    color: t.relation('color'),
    discounts: t.relation('discounts'),
    orderItems: t.relation('orderItems'),
    cartItems: t.relation('cartItems'),

    // Computed fields
    isLowStock: t.boolean({
      resolve: (variant) => variant.quantity <= variant.lowStockThreshold
    }),

    isInStock: t.boolean({
      resolve: (variant) => variant.quantity > 0
    }),
  }),
})

// Create connection types
export const { Connection: ProductVariantConnection, Edge: ProductVariantEdge } = createConnection('ProductVariant', ProductVariant)