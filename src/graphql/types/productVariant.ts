import { builder } from '../../lib/builder'

// ProductVariant Object Type
export const ProductVariantType = builder.prismaObject('ProductVariant', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productId: t.exposeString('productId'),
    sizeId: t.exposeString('sizeId'),
    colorId: t.exposeString('colorId'),
    sku: t.exposeString('sku'),
    price: t.exposeFloat('price'),
    quantity: t.exposeInt('quantity'),
    product: t.relation('product'),
    size: t.relation('size'),
    color: t.relation('color'),
    discounts: t.relatedConnection('discounts', {
      cursor: 'id',
      totalCount: true,
    }),
    orderItems: t.relatedConnection('orderItems', {
      cursor: 'id',
      totalCount: true,
    }),
    // Virtual field for current price after discounts
    currentPrice: t.field({
      type: 'Float',
      resolve: async (variant) => {
        const activeDiscounts = await builder.prisma.variantDiscount.findMany({
          where: {
            productVariantId: variant.id,
            discount: {
              startDate: { lte: new Date() },
              endDate: { gte: new Date() },
            },
          },
          include: {
            discount: true,
          },
        })

        if (activeDiscounts.length === 0) {
          return Number(variant.price)
        }

        let finalPrice = Number(variant.price)
        let totalPercentDiscount = 0
        let totalAmountDiscount = 0

        activeDiscounts.forEach(({ discount }) => {
          if (discount.discountPercent) {
            totalPercentDiscount += Number(discount.discountPercent)
          }
          if (discount.discountAmount) {
            totalAmountDiscount += Number(discount.discountAmount)
          }
        })

        // Apply percentage discounts first
        if (totalPercentDiscount > 0) {
          finalPrice = finalPrice * (1 - Math.min(totalPercentDiscount, 100) / 100)
        }

        // Apply amount discounts
        finalPrice = Math.max(0, finalPrice - totalAmountDiscount)

        return finalPrice
      },
    }),
    // Virtual field for availability
    isAvailable: t.field({
      type: 'Boolean',
      resolve: (variant) => variant.quantity > 0,
    }),
  }),
})

// ProductVariant Input Types
export const CreateProductVariantInput = builder.inputType('CreateProductVariantInput', {
  fields: (t) => ({
    productId: t.string({ required: true }),
    sizeId: t.string({ required: true }),
    colorId: t.string({ required: true }),
    sku: t.string({ required: true }),
    price: t.float({ required: true }),
    quantity: t.int({ required: true }),
  }),
})

export const UpdateProductVariantInput = builder.inputType('UpdateProductVariantInput', {
  fields: (t) => ({
    id: t.string({ required: true }),
    sku: t.string({ required: false }),
    price: t.float({ required: false }),
    quantity: t.int({ required: false }),
  }),
})

export const ProductVariantFilterInput = builder.inputType('ProductVariantFilterInput', {
  fields: (t) => ({
    productId: t.string({ required: false }),
    sizeId: t.string({ required: false }),
    colorId: t.string({ required: false }),
    inStock: t.boolean({ required: false }),
    priceMin: t.float({ required: false }),
    priceMax: t.float({ required: false }),
  }),
})

// ProductVariant Queries
builder.queryField('productVariant', (t) =>
  t.prismaField({
    type: 'ProductVariant',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      builder.prisma.productVariant.findUnique({
        ...query,
        where: { id: args.id },
      }),
  })
)

builder.queryField('productVariantBySku', (t) =>
  t.prismaField({
    type: 'ProductVariant',
    nullable: true,
    args: {
      sku: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      builder.prisma.productVariant.findUnique({
        ...query,
        where: { sku: args.sku },
      }),
  })
)

builder.queryField('productVariants', (t) =>
  t.prismaConnection({
    type: 'ProductVariant',
    cursor: 'id',
    totalCount: true,
    args: {
      filter: t.arg({ type: ProductVariantFilterInput, required: false }),
    },
    resolve: (query, _parent, args) => {
      const where: any = {}

      if (args.filter) {
        if (args.filter.productId) where.productId = args.filter.productId
        if (args.filter.sizeId) where.sizeId = args.filter.sizeId
        if (args.filter.colorId) where.colorId = args.filter.colorId
        if (args.filter.inStock) where.quantity = { gt: 0 }
        if (args.filter.priceMin !== undefined || args.filter.priceMax !== undefined) {
          where.price = {
            ...(args.filter.priceMin !== undefined && { gte: args.filter.priceMin }),
            ...(args.filter.priceMax !== undefined && { lte: args.filter.priceMax }),
          }
        }
      }

      return builder.prisma.productVariant.findMany({
        ...query,
        where,
        orderBy: { sku: 'asc' },
      })
    },
  })
)

// ProductVariant Mutations
builder.mutationField('createProductVariant', (t) =>
  t.prismaField({
    type: 'ProductVariant',
    args: {
      input: t.arg({ type: CreateProductVariantInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      // Validate references exist
      const [product, size, color] = await Promise.all([
        builder.prisma.product.findUnique({ where: { id: args.input.productId } }),
        builder.prisma.size.findUnique({ where: { id: args.input.sizeId } }),
        builder.prisma.color.findUnique({ where: { id: args.input.colorId } }),
      ])

      if (!product) throw new Error('Product not found')
      if (!size) throw new Error('Size not found')
      if (!color) throw new Error('Color not found')

      // Check if variant combination already exists
      const existingVariant = await builder.prisma.productVariant.findUnique({
        where: {
          productId_sizeId_colorId: {
            productId: args.input.productId,
            sizeId: args.input.sizeId,
            colorId: args.input.colorId,
          },
        },
      })

      if (existingVariant) {
        throw new Error('A variant with this product, size, and color combination already exists')
      }

      return builder.prisma.productVariant.create({
        ...query,
        data: {
          productId: args.input.productId,
          sizeId: args.input.sizeId,
          colorId: args.input.colorId,
          sku: args.input.sku,
          price: args.input.price,
          quantity: args.input.quantity,
        },
      })
    },
  })
)

builder.mutationField('updateProductVariant', (t) =>
  t.prismaField({
    type: 'ProductVariant',
    args: {
      input: t.arg({ type: UpdateProductVariantInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      const updateData: any = {}
      
      if (args.input.sku) updateData.sku = args.input.sku
      if (args.input.price !== undefined) updateData.price = args.input.price
      if (args.input.quantity !== undefined) updateData.quantity = args.input.quantity

      return builder.prisma.productVariant.update({
        ...query,
        where: { id: args.input.id },
        data: updateData,
      })
    },
  })
)

builder.mutationField('updateProductVariantQuantity', (t) =>
  t.prismaField({
    type: 'ProductVariant',
    args: {
      id: t.arg.string({ required: true }),
      quantity: t.arg.int({ required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.productVariant.update({
        ...query,
        where: { id: args.id },
        data: { quantity: args.quantity },
      })
    },
  })
)

builder.mutationField('deleteProductVariant', (t) =>
  t.prismaField({
    type: 'ProductVariant',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.productVariant.delete({
        ...query,
        where: { id: args.id },
      })
    },
  })
)
