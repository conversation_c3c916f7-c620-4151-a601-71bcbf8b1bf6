/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { builder } from '../../lib/builder';
import { z } from 'zod';

// Customer input types
export const CreateCustomerInput = builder.inputType('CreateCustomerInput', {
  fields: (t) => ({
    email: t.string({ required: true }),
    firstName: t.string({ required: true }),
    lastName: t.string({ required: true }),
    phone: t.string(),
  }),
});

export const UpdateCustomerInput = builder.inputType('UpdateCustomerInput', {
  fields: (t) => ({
    firstName: t.string(),
    lastName: t.string(),
    phone: t.string(),
  }),
});

// Product input types
export const CreateProductInput = builder.inputType('CreateProductInput', {
  fields: (t) => ({
    name: t.string({ required: true }),
    description: t.string({ required: true }),
    categoryId: t.string({ required: true }),
  }),
});

export const UpdateProductInput = builder.inputType('UpdateProductInput', {
  fields: (t) => ({
    name: t.string(),
    description: t.string(),
    categoryId: t.string(),
  }),
});

// Order input types
export const CreateOrderInput = builder.inputType('CreateOrderInput', {
  fields: (t) => ({
    customerId: t.string({ required: true }),
    items: t.field({
      type: [builder.inputType('OrderItemInput', {
        fields: (t) => ({
          productVariantId: t.string({ required: true }),
          quantity: t.int({ required: true }),
          priceAtPurchase: t.float({ required: true }),
        }),
      })],
      required: true,
    }),
    totalAmount: t.float({ required: true }),
  }),
});

export const UpdateOrderStatusInput = builder.inputType('UpdateOrderStatusInput', {
  fields: (t) => ({
    status: t.field({ type: 'String', required: true }),
  }),
});

// Review input types
export const CreateReviewInput = builder.inputType('CreateReviewInput', {
  fields: (t) => ({
    productId: t.string({ required: true }),
    customerId: t.string({ required: true }),
    rating: t.int({ required: true }),
    comment: t.string(),
    images: t.stringList(),
  }),
});

export const UpdateReviewInput = builder.inputType('UpdateReviewInput', {
  fields: (t) => ({
    rating: t.int(),
    comment: t.string(),
    images: t.stringList(),
  }),
});

// Order status enum for input
export const OrderStatusInput = builder.enumType('OrderStatusInput', {
  values: {
    PENDING: { value: 'pending' },
    SHIPPED: { value: 'shipped' },
    DELIVERED: { value: 'delivered' },
    CANCELLED: { value: 'cancelled' },
  },
});

// Validation schemas
const CreateCustomerSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1).max(100),
  lastName: z.string().min(1).max(100),
  phone: z.string().optional(),
});

const UpdateCustomerSchema = z.object({
  firstName: z.string().min(1).max(100).optional(),
  lastName: z.string().min(1).max(100).optional(),
  phone: z.string().optional(),
});

const CreateProductSchema = z.object({
  name: z.string().min(1).max(200),
  description: z.string().min(1),
  categoryId: z.string(),
});

const CreateOrderSchema = z.object({
  customerId: z.string(),
  items: z.array(z.object({
    productVariantId: z.string(),
    quantity: z.number().int().positive(),
    priceAtPurchase: z.number().positive(),
  })).min(1),
  totalAmount: z.number().positive(),
});

const CreateReviewSchema = z.object({
  productId: z.string(),
  customerId: z.string(),
  rating: z.number().int().min(1).max(5),
  comment: z.string().optional(),
  images: z.array(z.string()).optional(),
});

// Mutations
builder.mutationFields((t) => ({
  // Customer mutations temporarily removed to fix build issues

  // Product mutations
  createProduct: t.prismaField({
    type: 'Product',
    args: {
      input: t.arg({ type: CreateProductInput, required: true }),
    },
    validate: {
      schema: z.object({
        input: CreateProductSchema,
      }),
    },
    resolve: async (query, _, args, ctx) => {
      // Verify category exists
      const category = await ctx.prisma.category.findUnique({
        where: { id: args.input.categoryId },
      });

      if (!category) {
        throw new Error('Category not found');
      }

      return ctx.prisma.product.create({
        ...query,
        data: args.input,
      });
    },
  }),

  updateProduct: t.prismaField({
    type: 'Product',
    args: {
      id: t.arg.string({ required: true }),
      input: t.arg({ type: UpdateProductInput, required: true }),
    },
    resolve: async (query, _, args, ctx) => {
      // Filter out undefined values from input
      const updateData: any = {};
      if (args.input.name !== undefined) {
        updateData.name = args.input.name;
      }
      if (args.input.description !== undefined) {
        updateData.description = args.input.description;
      }
      if (args.input.categoryId !== undefined) {
        // Verify category exists
        const category = await ctx.prisma.category.findUnique({
          where: { id: args.input.categoryId! },
        });
        if (!category) {
          throw new Error('Category not found');
        }
        updateData.categoryId = args.input.categoryId;
      }

      return ctx.prisma.product.update({
        ...query,
        where: { id: args.id },
        data: updateData,
      });
    },
  }),

  deleteProduct: t.field({
    type: 'Boolean',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (_, args, ctx) => {
      try {
        await ctx.prisma.product.delete({
          where: { id: args.id },
        });
        return true;
      } catch (error) {
        throw new Error('Failed to delete product');
      }
    },
  }),

  // Order mutations
  createOrder: t.prismaField({
    type: 'Order',
    args: {
      input: t.arg({ type: CreateOrderInput, required: true }),
    },
    validate: {
      schema: z.object({
        input: CreateOrderSchema,
      }),
    },
    resolve: async (query, _, args, ctx) => {
      // Verify customer exists
      const customer = await ctx.prisma.customer.findUnique({
        where: { id: args.input.customerId },
      });

      if (!customer) {
        throw new Error('Customer not found');
      }

      // Verify all product variants exist and have sufficient stock
      for (const item of args.input.items) {
        const variant = await ctx.prisma.productVariant.findUnique({
          where: { id: item.productVariantId },
        });

        if (!variant) {
          throw new Error(`Product variant ${item.productVariantId} not found`);
        }

        if (variant.quantity < item.quantity) {
          throw new Error(`Insufficient stock for product variant ${item.productVariantId}`);
        }
      }

      // Create order with items
      const order = await ctx.prisma.order.create({
        ...query,
        data: {
          customerId: args.input.customerId,
          orderDate: new Date(),
          totalAmount: args.input.totalAmount,
          status: 'pending',
          items: {
            create: args.input.items.map(item => ({
              productVariantId: item.productVariantId,
              quantity: item.quantity,
              priceAtPurchase: item.priceAtPurchase,
            })),
          },
        },
      });

      // Update product variant quantities
      for (const item of args.input.items) {
        await ctx.prisma.productVariant.update({
          where: { id: item.productVariantId },
          data: {
            quantity: {
              decrement: item.quantity,
            },
          },
        });
      }

      return order;
    },
  }),

  updateOrderStatus: t.prismaField({
    type: 'Order',
    args: {
      id: t.arg.string({ required: true }),
      input: t.arg({ type: UpdateOrderStatusInput, required: true }),
    },
    resolve: async (query, _, args, ctx) => {
      return ctx.prisma.order.update({
        ...query,
        where: { id: args.id },
        data: {
          status: args.input.status as any,
        },
      });
    },
  }),

  cancelOrder: t.prismaField({
    type: 'Order',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, _, args, ctx) => {
      // Get order with items
      const order = await ctx.prisma.order.findUnique({
        where: { id: args.id },
        include: { items: true },
      });

      if (!order) {
        throw new Error('Order not found');
      }

      if (order.status === 'delivered' || order.status === 'cancelled') {
        throw new Error('Cannot cancel this order');
      }

      // Restore product variant quantities
      for (const item of order.items) {
        await ctx.prisma.productVariant.update({
          where: { id: item.productVariantId },
          data: {
            quantity: {
              increment: item.quantity,
            },
          },
        });
      }

      return ctx.prisma.order.update({
        ...query,
        where: { id: args.id },
        data: {
          status: 'cancelled',
        },
      });
    },
  }),

  // Review mutations
  createReview: t.prismaField({
    type: 'Review',
    args: {
      input: t.arg({ type: CreateReviewInput, required: true }),
    },
    validate: {
      schema: z.object({
        input: CreateReviewSchema,
      }),
    },
    resolve: async (query, _, args, ctx) => {
      // Check if customer has already reviewed this product
      const existingReview = await ctx.prisma.review.findFirst({
        where: {
          productId: args.input.productId,
          customerId: args.input.customerId,
        },
      });

      if (existingReview) {
        throw new Error('Customer has already reviewed this product');
      }

      // Verify product and customer exist
      const product = await ctx.prisma.product.findUnique({
        where: { id: args.input.productId },
      });
      const customer = await ctx.prisma.customer.findUnique({
        where: { id: args.input.customerId },
      });

      if (!product) {
        throw new Error('Product not found');
      }
      if (!customer) {
        throw new Error('Customer not found');
      }

      return ctx.prisma.review.create({
        ...query,
        data: {
          productId: args.input.productId,
          customerId: args.input.customerId,
          rating: args.input.rating,
          comment: args.input.comment || undefined,
          images: args.input.images || [],
        },
      });
    },
  }),

  updateReview: t.prismaField({
    type: 'Review',
    args: {
      id: t.arg.string({ required: true }),
      input: t.arg({ type: UpdateReviewInput, required: true }),
    },
    resolve: async (query, _, args, ctx) => {
      const updateData: any = {};
      if (args.input.rating !== undefined) {
        updateData.rating = args.input.rating;
      }
      if (args.input.comment !== undefined) {
        updateData.comment = args.input.comment;
      }
      if (args.input.images !== undefined) {
        updateData.images = args.input.images;
      }

      return ctx.prisma.review.update({
        ...query,
        where: { id: args.id },
        data: updateData,
      });
    },
  }),

  deleteReview: t.field({
    type: 'Boolean',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (_, args, ctx) => {
      try {
        await ctx.prisma.review.delete({
          where: { id: args.id },
        });
        return true;
      } catch (error) {
        throw new Error('Failed to delete review');
      }
    },
  }),

  // Wishlist mutations
  addToWishlist: t.prismaField({
    type: 'WishlistItem',
    args: {
      customerId: t.arg.string({ required: true }),
      productVariantId: t.arg.string({ required: true }),
    },
    resolve: async (query, _, args, ctx) => {
      // Get or create wishlist for customer
      let wishlist = await ctx.prisma.wishlist.findFirst({
        where: { customerId: args.customerId },
      });

      if (!wishlist) {
        wishlist = await ctx.prisma.wishlist.create({
          data: {
            customerId: args.customerId,
          },
        });
      }

      // Check if item already exists in wishlist
      const existingItem = await ctx.prisma.wishlistItem.findFirst({
        where: {
          wishlistId: wishlist.id,
          productVariantId: args.productVariantId,
        },
      });

      if (existingItem) {
        throw new Error('Item already in wishlist');
      }

      return ctx.prisma.wishlistItem.create({
        ...query,
        data: {
          wishlistId: wishlist.id,
          productVariantId: args.productVariantId,
        },
      });
    },
  }),

  removeFromWishlist: t.field({
    type: 'Boolean',
    args: {
      customerId: t.arg.string({ required: true }),
      productVariantId: t.arg.string({ required: true }),
    },
    resolve: async (_, args, ctx) => {
      try {
        const wishlist = await ctx.prisma.wishlist.findFirst({
          where: { customerId: args.customerId },
        });

        if (!wishlist) {
          throw new Error('Wishlist not found');
        }

        await ctx.prisma.wishlistItem.deleteMany({
          where: {
            wishlistId: wishlist.id,
            productVariantId: args.productVariantId,
          },
        });

        return true;
      } catch (error) {
        throw new Error('Failed to remove item from wishlist');
      }
    },
  }),
}));
