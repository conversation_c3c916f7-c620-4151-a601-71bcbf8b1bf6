import { builder } from '../../lib/builder'

// Enums
export const OrderStatus = builder.enumType('OrderStatus', {
  values: {
    PENDING: { value: 'PENDING' },
    CONFIRMED: { value: 'CONFIRMED' },
    PROCESSING: { value: 'PROCESSING' },
    SHIPPED: { value: 'SHIPPED' },
    DELIVERED: { value: 'DELIVERED' },
    CANCELLED: { value: 'CANCELLED' },
    REFUNDED: { value: 'REFUNDED' },
  },
})

export const PaymentStatus = builder.enumType('PaymentStatus', {
  values: {
    PENDING: { value: 'PENDING' },
    PAID: { value: 'PAID' },
    FAILED: { value: 'FAILED' },
    REFUNDED: { value: 'REFUNDED' },
    PARTIALLY_REFUNDED: { value: 'PARTIALLY_REFUNDED' },
  },
})

export const DiscountType = builder.enumType('DiscountType', {
  values: {
    PERCENTAGE: { value: 'PERCENTAGE' },
    FIXED_AMOUNT: { value: 'FIXED_AMOUNT' },
  },
})

export const AddressType = builder.enumType('AddressType', {
  values: {
    SHIPPING: { value: 'SHIPPING' },
    BILLING: { value: 'BILLING' },
  },
})

export const Gender = builder.enumType('Gender', {
  values: {
    MALE: { value: 'MALE' },
    FEMALE: { value: 'FEMALE' },
    OTHER: { value: 'OTHER' },
    PREFER_NOT_TO_SAY: { value: 'PREFER_NOT_TO_SAY' },
  },
})

export const OrderDirection = builder.enumType('OrderDirection', {
  values: {
    ASC: { value: 'asc' },
    DESC: { value: 'desc' },
  },
})

// Cart type
export const Cart = builder.prismaObject('Cart', {
  fields: (t) => ({
    id: t.exposeID('id'),
    customerId: t.exposeString('customerId'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    customer: t.relation('customer'),
    items: t.relation('items'),
  }),
})

// CartItem type
export const CartItem = builder.prismaObject('CartItem', {
  fields: (t) => ({
    id: t.exposeID('id'),
    cartId: t.exposeString('cartId'),
    productVariantId: t.exposeString('productVariantId'),
    quantity: t.exposeInt('quantity'),
    addedAt: t.expose('addedAt', { type: 'DateTime' }),
    cart: t.relation('cart'),
    productVariant: t.relation('productVariant'),
  }),
})

// Wishlist type
export const Wishlist = builder.prismaObject('Wishlist', {
  fields: (t) => ({
    id: t.exposeID('id'),
    customerId: t.exposeString('customerId'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    customer: t.relation('customer'),
    items: t.relation('items'),
  }),
})

// WishlistItem type
export const WishlistItem = builder.prismaObject('WishlistItem', {
  fields: (t) => ({
    id: t.exposeID('id'),
    wishlistId: t.exposeString('wishlistId'),
    productId: t.exposeString('productId'),
    addedAt: t.expose('addedAt', { type: 'DateTime' }),
    wishlist: t.relation('wishlist'),
    product: t.relation('product'),
  }),
})

// Discount type
export const Discount = builder.prismaObject('Discount', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    description: t.exposeString('description', { nullable: true }),
    code: t.exposeString('code', { nullable: true }),
    discountType: t.expose('discountType', { type: DiscountType }),
    discountPercent: t.exposeFloat('discountPercent', { nullable: true }),
    discountAmount: t.exposeFloat('discountAmount', { nullable: true }),
    minimumAmount: t.exposeFloat('minimumAmount', { nullable: true }),
    maximumDiscount: t.exposeFloat('maximumDiscount', { nullable: true }),
    usageLimit: t.exposeInt('usageLimit', { nullable: true }),
    usageCount: t.exposeInt('usageCount'),
    isActive: t.exposeBoolean('isActive'),
    startDate: t.expose('startDate', { type: 'DateTime' }),
    endDate: t.expose('endDate', { type: 'DateTime' }),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    variants: t.relation('variants'),
    orders: t.relation('orders'),
  }),
})

// VariantDiscount type
export const VariantDiscount = builder.prismaObject('VariantDiscount', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productVariantId: t.exposeString('productVariantId'),
    discountId: t.exposeString('discountId'),
    productVariant: t.relation('productVariant'),
    discount: t.relation('discount'),
  }),
})

// Address type
export const Address = builder.prismaObject('Address', {
  fields: (t) => ({
    id: t.exposeID('id'),
    customerId: t.exposeString('customerId'),
    type: t.expose('type', { type: AddressType }),
    firstName: t.exposeString('firstName'),
    lastName: t.exposeString('lastName'),
    company: t.exposeString('company', { nullable: true }),
    address1: t.exposeString('address1'),
    address2: t.exposeString('address2', { nullable: true }),
    city: t.exposeString('city'),
    state: t.exposeString('state'),
    postalCode: t.exposeString('postalCode'),
    country: t.exposeString('country'),
    phone: t.exposeString('phone', { nullable: true }),
    isDefault: t.exposeBoolean('isDefault'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    customer: t.relation('customer'),
  }),
})

// Order type
export const Order = builder.prismaObject('Order', {
  fields: (t) => ({
    id: t.exposeID('id'),
    orderNumber: t.exposeString('orderNumber'),
    customerId: t.exposeString('customerId'),
    orderDate: t.expose('orderDate', { type: 'DateTime' }),
    subtotalAmount: t.exposeFloat('subtotalAmount'),
    taxAmount: t.exposeFloat('taxAmount'),
    shippingAmount: t.exposeFloat('shippingAmount'),
    discountAmount: t.exposeFloat('discountAmount'),
    totalAmount: t.exposeFloat('totalAmount'),
    status: t.expose('status', { type: OrderStatus }),
    paymentStatus: t.expose('paymentStatus', { type: PaymentStatus }),
    shippingMethod: t.exposeString('shippingMethod', { nullable: true }),
    trackingNumber: t.exposeString('trackingNumber', { nullable: true }),
    notes: t.exposeString('notes', { nullable: true }),
    shippingAddress: t.expose('shippingAddress', { type: 'JSON' }),
    billingAddress: t.expose('billingAddress', { type: 'JSON', nullable: true }),
    discountId: t.exposeString('discountId', { nullable: true }),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    customer: t.relation('customer'),
    discount: t.relation('discount', { nullable: true }),
    items: t.relation('items'),
  }),
})

// OrderItem type
export const OrderItem = builder.prismaObject('OrderItem', {
  fields: (t) => ({
    id: t.exposeID('id'),
    orderId: t.exposeString('orderId'),
    productVariantId: t.exposeString('productVariantId'),
    quantity: t.exposeInt('quantity'),
    priceAtPurchase: t.exposeFloat('priceAtPurchase'),
    discountAmount: t.exposeFloat('discountAmount'),
    productSnapshot: t.expose('productSnapshot', { type: 'JSON' }),
    order: t.relation('order'),
    productVariant: t.relation('productVariant'),
  }),
})

// Size type
export const Size = builder.prismaObject('Size', {
  fields: (t) => ({
    id: t.exposeID('id'),
    sizeLabel: t.exposeString('sizeLabel'),
    category: t.exposeString('category', { nullable: true }),
    sortOrder: t.exposeInt('sortOrder'),
    isActive: t.exposeBoolean('isActive'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    variants: t.relation('variants'),
  }),
})

// Color type
export const Color = builder.prismaObject('Color', {
  fields: (t) => ({
    id: t.exposeID('id'),
    colorName: t.exposeString('colorName'),
    hexCode: t.exposeString('hexCode', { nullable: true }),
    isActive: t.exposeBoolean('isActive'),
    sortOrder: t.exposeInt('sortOrder'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    variants: t.relation('variants'),
  }),
})

// ProductImage type
export const ProductImage = builder.prismaObject('ProductImage', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productId: t.exposeString('productId'),
    imageUrl: t.exposeString('imageUrl'),
    altText: t.exposeString('altText', { nullable: true }),
    isPrimary: t.exposeBoolean('isPrimary'),
    sortOrder: t.exposeInt('sortOrder'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    product: t.relation('product'),
  }),
})

// ProductReview type
export const ProductReview = builder.prismaObject('ProductReview', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productId: t.exposeString('productId'),
    customerId: t.exposeString('customerId'),
    rating: t.exposeInt('rating'),
    title: t.exposeString('title', { nullable: true }),
    comment: t.exposeString('comment', { nullable: true }),
    isVerified: t.exposeBoolean('isVerified'),
    isApproved: t.exposeBoolean('isApproved'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    product: t.relation('product'),
    customer: t.relation('customer'),
  }),
})