/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { builder } from '../../lib/builder';

// Basic enum types
export const Gender = builder.enumType('Gender', {
  values: {
    MALE: { value: 'MALE' },
    FEMALE: { value: 'FEMALE' },
    OTHER: { value: 'OTHER' },
    PREFER_NOT_TO_SAY: { value: 'PREFER_NOT_TO_SAY' },
  },
});

export const OrderDirection = builder.enumType('OrderDirection', {
  values: {
    ASC: { value: 'asc' },
    DESC: { value: 'desc' },
  },
});

export const AddressType = builder.enumType('AddressType', {
  values: {
    BILLING: { value: 'BILLING' },
    SHIPPING: { value: 'SHIPPING' },
  },
});

export const PaymentStatus = builder.enumType('PaymentStatus', {
  values: {
    PENDING: { value: 'PENDING' },
    COMPLETED: { value: 'COMPLETED' },
    FAILED: { value: 'FAILED' },
    REFUNDED: { value: 'REFUNDED' },
  },
});

export const DiscountType = builder.enumType('DiscountType', {
  values: {
    PERCENTAGE: { value: 'PERCENTAGE' },
    FIXED_AMOUNT: { value: 'FIXED_AMOUNT' },
  },
});

// Size and Color types
export const Size = builder.prismaObject('Size', {
  fields: (t) => ({
    id: t.exposeID('id'),
    sizeLabel: t.exposeString('sizeLabel'),
    category: t.exposeString('category', { nullable: true }),
    sortOrder: t.exposeInt('sortOrder', { nullable: true }),
    isActive: t.exposeBoolean('isActive'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    variants: t.relation('variants'),
  }),
});

export const Color = builder.prismaObject('Color', {
  fields: (t) => ({
    id: t.exposeID('id'),
    colorName: t.exposeString('colorName'),
    hexCode: t.exposeString('hexCode', { nullable: true }),
    sortOrder: t.exposeInt('sortOrder', { nullable: true }),
    isActive: t.exposeBoolean('isActive'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    variants: t.relation('variants'),
  }),
});
