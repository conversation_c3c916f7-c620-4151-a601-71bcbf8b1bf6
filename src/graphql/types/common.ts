/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { builder } from '../../lib/builder';

// Basic enum types
export const Gender = builder.enumType('Gender', {
  values: {
    MALE: { value: 'MALE' },
    FEMALE: { value: 'FEMALE' },
    OTHER: { value: 'OTHER' },
    PREFER_NOT_TO_SAY: { value: 'PREFER_NOT_TO_SAY' },
  },
});

export const OrderDirection = builder.enumType('OrderDirection', {
  values: {
    ASC: { value: 'asc' },
    DESC: { value: 'desc' },
  },
});

export const AddressType = builder.enumType('AddressType', {
  values: {
    BILLING: { value: 'BILLING' },
    SHIPPING: { value: 'SHIPPING' },
  },
});

export const PaymentStatus = builder.enumType('PaymentStatus', {
  values: {
    PENDING: { value: 'PENDING' },
    COMPLETED: { value: 'COMPLETED' },
    FAILED: { value: 'FAILED' },
    REFUNDED: { value: 'REFUNDED' },
  },
});

export const DiscountType = builder.enumType('DiscountType', {
  values: {
    PERCENTAGE: { value: 'PERCENTAGE' },
    FIXED_AMOUNT: { value: 'FIXED_AMOUNT' },
  },
});

// Size and Color types
export const Size = builder.prismaObject('Size', {
  fields: (t) => ({
    id: t.exposeID('id'),
    sizeLabel: t.exposeString('sizeLabel'),
    category: t.exposeString('category', { nullable: true }),
    sortOrder: t.exposeInt('sortOrder', { nullable: true }),
    isActive: t.exposeBoolean('isActive'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    variants: t.relation('variants'),
  }),
});

export const Color = builder.prismaObject('Color', {
  fields: (t) => ({
    id: t.exposeID('id'),
    colorName: t.exposeString('colorName'),
    hexCode: t.exposeString('hexCode', { nullable: true }),
    sortOrder: t.exposeInt('sortOrder', { nullable: true }),
    isActive: t.exposeBoolean('isActive'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    variants: t.relation('variants'),
  }),
});

// Tag and ProductTag types
export const Tag = builder.prismaObject('Tag', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    productTags: t.relation('productTags'),
  }),
});

export const ProductTag = builder.prismaObject('ProductTag', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productId: t.exposeString('productId'),
    tagId: t.exposeString('tagId'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    product: t.relation('product'),
    tag: t.relation('tag'),
  }),
});

// Discount and VariantDiscount types
export const Discount = builder.prismaObject('Discount', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    description: t.exposeString('description', { nullable: true }),
    discountPercent: t.exposeInt('discountPercent', { nullable: true }),
    discountAmount: t.exposeInt('discountAmount', { nullable: true }),
    startDate: t.expose('startDate', { type: 'DateTime' }),
    endDate: t.expose('endDate', { type: 'DateTime' }),
    variants: t.relation('variants'),
  }),
});

export const VariantDiscount = builder.prismaObject('VariantDiscount', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productVariantId: t.exposeString('productVariantId'),
    discountId: t.exposeString('discountId'),
    productVariant: t.relation('productVariant'),
    discount: t.relation('discount'),
  }),
});
