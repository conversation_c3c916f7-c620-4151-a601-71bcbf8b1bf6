import { builder } from '../../lib/builder'

// Enums
export const OrderStatus = builder.enumType('OrderStatus', {
  values: {
    PENDING: { value: 'PENDING' },
    CONFIRMED: { value: 'CONFIRMED' },
    PROCESSING: { value: 'PROCESSING' },
    SHIPPED: { value: 'SHIPPED' },
    DELIVERED: { value: 'DELIVERED' },
    CANCELLED: { value: 'CANCELLED' },
    REFUNDED: { value: 'REFUNDED' },
  },
})

export const PaymentStatus = builder.enumType('PaymentStatus', {
  values: {
    PENDING: { value: 'PENDING' },
    PAID: { value: 'PAID' },
    FAILED: { value: 'FAILED' },
    REFUNDED: { value: 'REFUNDED' },
    PARTIALLY_REFUNDED: { value: 'PARTIALLY_REFUNDED' },
  },
})

export const DiscountType = builder.enumType('DiscountType', {
  values: {
    PERCENTAGE: { value: 'PERCENTAGE' },
    FIXED_AMOUNT: { value: 'FIXED_AMOUNT' },
  },
})

export const AddressType = builder.enumType('AddressType', {
  values: {
    SHIPPING: { value: 'SHIPPING' },
    BILLING: { value: 'BILLING' },
  },
})

export const Gender = builder.enumType('Gender', {
  values: {
    MALE: { value: 'MALE' },
    FEMALE: { value: 'FEMALE' },
    OTHER: { value: 'OTHER' },
    PREFER_NOT_TO_SAY: { value: 'PREFER_NOT_TO_SAY' },
  },
})

// Size type
export const Size = builder.prismaObject('Size', {
  fields: (t) => ({
    id: t.exposeID('id'),
    sizeLabel: t.exposeString('sizeLabel'),
    category: t.exposeString('category', { nullable: true }),
    sortOrder: t.exposeInt('sortOrder'),
    isActive: t.exposeBoolean('isActive'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    variants: t.relation('variants'),
  }),
})

// Color type
export const Color = builder.prismaObject('Color', {
  fields: (t) => ({
    id: t.exposeID('id'),
    colorName: t.exposeString('colorName'),
    hexCode: t.exposeString('hexCode', { nullable: true }),
    isActive: t.exposeBoolean('isActive'),
    sortOrder: t.exposeInt('sortOrder'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    variants: t.relation('variants'),
  }),
})

// ProductImage type
export const ProductImage = builder.prismaObject('ProductImage', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productId: t.exposeString('productId'),
    imageUrl: t.exposeString('imageUrl'),
    altText: t.exposeString('altText', { nullable: true }),
    isPrimary: t.exposeBoolean('isPrimary'),
    sortOrder: t.exposeInt('sortOrder'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    product: t.relation('product'),
  }),
})

// ProductReview type
export const ProductReview = builder.prismaObject('ProductReview', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productId: t.exposeString('productId'),
    customerId: t.exposeString('customerId'),
    rating: t.exposeInt('rating'),
    title: t.exposeString('title', { nullable: true }),
    comment: t.exposeString('comment', { nullable: true }),
    isVerified: t.exposeBoolean('isVerified'),
    isApproved: t.exposeBoolean('isApproved'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    product: t.relation('product'),
    customer: t.relation('customer'),
  }),
})