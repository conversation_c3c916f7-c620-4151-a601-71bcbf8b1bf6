import { builder } from '../../lib/builder'
// Page Info Type
export const PageInfoType = builder.simpleObject('PageInfo', {
  fields: (t) => ({
    page: t.int({ description: 'Current page number' }),
    pageSize: t.int({ description: 'Items per page' }),
    totalPages: t.int({ description: 'Total number of pages' }),
    totalCount: t.int({ description: 'Total number of items' }),
    hasNextPage: t.boolean({ description: 'Whether there is a next page' }),
    hasPreviousPage: t.boolean({ description: 'Whether there is a previous page' }),
  }),
})

// Shared enum for sort direction used across multiple types
export const SortDirection = builder.enumType('SortDirection', {
  values: {
    ASC: {},
    DESC: {},
  },
})

// Pagination Input Type
export const PaginationInput = builder.inputType('PaginationInput', {
  fields: (t) => ({
    page: t.int({ required: false, description: 'Page number (1-based)' }),
    pageSize: t.int({ required: false, description: 'Number of items per page (default: 10, max: 100)' }),
    offset: t.int({ required: false, description: 'Number of items to skip' }),
    skip: t.int({ required: false, description: 'Alias for offset' }),
  }),
})

// String Filter Input
export const StringFilterInput = builder.inputType('StringFilterInput', {
  fields: (t) => ({
    equals: t.string({ required: false }),
    not: t.string({ required: false }),
    in: t.stringList({ required: false }),
    notIn: t.stringList({ required: false }),
    contains: t.string({ required: false }),
    startsWith: t.string({ required: false }),
    endsWith: t.string({ required: false }),
    mode: t.field({ type: StringFilterMode, required: false }),
  }),
})

export const StringFilterMode = builder.enumType('StringFilterMode', {
  values: {
    default: {},
    insensitive: {},
  },
})

// Number Filter Input
export const FloatFilterInput = builder.inputType('FloatFilterInput', {
  fields: (t) => ({
    equals: t.float({ required: false }),
    not: t.float({ required: false }),
    in: t.floatList({ required: false }),
    notIn: t.floatList({ required: false }),
    lt: t.float({ required: false, description: 'Less than' }),
    lte: t.float({ required: false, description: 'Less than or equal' }),
    gt: t.float({ required: false, description: 'Greater than' }),
    gte: t.float({ required: false, description: 'Greater than or equal' }),
  }),
})

export const IntFilterInput = builder.inputType('IntFilterInput', {
  fields: (t) => ({
    equals: t.int({ required: false }),
    not: t.int({ required: false }),
    in: t.intList({ required: false }),
    notIn: t.intList({ required: false }),
    lt: t.int({ required: false, description: 'Less than' }),
    lte: t.int({ required: false, description: 'Less than or equal' }),
    gt: t.int({ required: false, description: 'Greater than' }),
    gte: t.int({ required: false, description: 'Greater than or equal' }),
  }),
})

// Date Filter Input
export const DateFilterInput = builder.inputType('DateFilterInput', {
  fields: (t) => ({
    equals: t.field({ type: 'DateTime', required: false }),
    not: t.field({ type: 'DateTime', required: false }),
    in: t.field({ type: ['DateTime'], required: false }),
    notIn: t.field({ type: ['DateTime'], required: false }),
    lt: t.field({ type: 'DateTime', required: false, description: 'Before this date' }),
    lte: t.field({ type: 'DateTime', required: false, description: 'Before or on this date' }),
    gt: t.field({ type: 'DateTime', required: false, description: 'After this date' }),
    gte: t.field({ type: 'DateTime', required: false, description: 'After or on this date' }),
  }),
})

// Boolean Filter Input
export const BooleanFilterInput = builder.inputType('BooleanFilterInput', {
  fields: (t) => ({
    equals: t.boolean({ required: false }),
    not: t.boolean({ required: false }),
  }),
})

// Helper function to calculate pagination
export const calculatePagination = (args: {
  page?: number | null
  pageSize?: number | null
  offset?: number | null
  skip?: number | null
}) => {
  const defaultPageSize = 10
  const maxPageSize = 100
  
  let pageSize = args.pageSize || defaultPageSize
  pageSize = Math.min(pageSize, maxPageSize)
  pageSize = Math.max(pageSize, 1)
  
  let skip = 0
  
  if (args.offset !== null && args.offset !== undefined) {
    skip = args.offset
  } else if (args.skip !== null && args.skip !== undefined) {
    skip = args.skip
  } else if (args.page !== null && args.page !== undefined) {
    const page = Math.max(args.page, 1)
    skip = (page - 1) * pageSize
  }
  
  return {
    take: pageSize,
    skip: Math.max(skip, 0),
    pageSize,
    page: Math.floor(skip / pageSize) + 1,
  }
}

// Helper function to create page info
export const createPageInfo = (
  totalCount: number,
  page: number,
  pageSize: number
) => ({
  page,
  pageSize,
  totalPages: Math.ceil(totalCount / pageSize),
  totalCount,
  hasNextPage: page * pageSize < totalCount,
  hasPreviousPage: page > 1,
})
