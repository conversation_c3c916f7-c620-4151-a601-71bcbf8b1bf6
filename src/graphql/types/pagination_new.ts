import { builder } from '../../lib/builder';
import { z } from 'zod';

// Cursor-based pagination input (Relay-style)
export const PageInfoInputSchema = z.object({
  first: z.number().int().positive().max(100).optional(),
  after: z.string().optional(),
  last: z.number().int().positive().max(100).optional(),
  before: z.string().optional(),
});

export const PageInfoInput = builder.inputType('PageInfoInput', {
  fields: (t) => ({
    first: t.int(),
    after: t.string(),
    last: t.int(),
    before: t.string(),
  }),
});

// Traditional pagination input
export const PaginationInputSchema = z.object({
  page: z.number().int().positive().default(1),
  pageSize: z.number().int().positive().max(100).default(10),
  offset: z.number().int().min(0).optional(),
  skip: z.number().int().min(0).optional(),
});

export const PaginationInput = builder.inputType('PaginationInput', {
  fields: (t) => ({
    page: t.int({ defaultValue: 1 }),
    pageSize: t.int({ defaultValue: 10 }),
    offset: t.int(),
    skip: t.int(),
  }),
});

// Relay PageInfo type
export const PageInfo = builder.objectType('PageInfo', {
  fields: (t) => ({
    hasNextPage: t.boolean(),
    hasPreviousPage: t.boolean(),
    startCursor: t.string({ nullable: true }),
    endCursor: t.string({ nullable: true }),
    totalCount: t.int({ nullable: true }),
  }),
});

// Traditional pagination info
export const Pagination = builder.objectType('Pagination', {
  fields: (t) => ({
    currentPage: t.int(),
    pageSize: t.int(),
    totalItems: t.int(),
    totalPages: t.int(),
    hasNextPage: t.boolean(),
    hasPreviousPage: t.boolean(),
  }),
});

// Sort direction enum
export const SortDirection = builder.enumType('SortDirection', {
  values: {
    ASC: { value: 'asc' },
    DESC: { value: 'desc' },
  },
});

// Generic sort input
export const SortInput = builder.inputType('SortInput', {
  fields: (t) => ({
    field: t.string({ required: true }),
    direction: t.field({ type: SortDirection, defaultValue: 'asc' }),
  }),
});

// Date range filter
export const DateRangeInput = builder.inputType('DateRangeInput', {
  fields: (t) => ({
    from: t.field({ type: 'DateTime' }),
    to: t.field({ type: 'DateTime' }),
  }),
});

// Number range filter
export const NumberRangeInput = builder.inputType('NumberRangeInput', {
  fields: (t) => ({
    min: t.float(),
    max: t.float(),
  }),
});

// String filter options
export const StringFilterInput = builder.inputType('StringFilterInput', {
  fields: (t) => ({
    equals: t.string(),
    contains: t.string(),
    startsWith: t.string(),
    endsWith: t.string(),
    in: t.stringList(),
    notIn: t.stringList(),
  }),
});

// Utility functions for pagination
export function calculateTraditionalPagination(
  page: number,
  pageSize: number,
  totalItems: number,
  offset?: number,
  skip?: number
) {
  const actualSkip = skip ?? offset ?? (page - 1) * pageSize;
  const totalPages = Math.ceil(totalItems / pageSize);
  
  return {
    skip: actualSkip,
    take: pageSize,
    pagination: {
      currentPage: page,
      pageSize,
      totalItems,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
}

export function calculateCursorPagination(
  args: {
    first?: number | null;
    after?: string | null;
    last?: number | null;
    before?: string | null;
  },
  totalCount?: number
) {
  const { first, after, last, before } = args;
  
  let skip = 0;
  let take = first || last || 10;
  
  if (after) {
    // Decode cursor and skip to the next item
    try {
      const decoded = Buffer.from(after, 'base64').toString('utf-8');
      const cursorData = JSON.parse(decoded);
      skip = cursorData.offset + 1;
    } catch {
      // Invalid cursor, start from beginning
      skip = 0;
    }
  }
  
  if (before) {
    try {
      const decoded = Buffer.from(before, 'base64').toString('utf-8');
      const cursorData = JSON.parse(decoded);
      skip = Math.max(0, cursorData.offset - take);
    } catch {
      skip = 0;
    }
  }
  
  if (last && !before) {
    // Get last N items
    skip = Math.max(0, (totalCount || 0) - last);
    take = last;
  }
  
  return { skip, take };
}

export function createCursor(id: string, offset: number): string {
  const cursorData = { id, offset };
  return Buffer.from(JSON.stringify(cursorData)).toString('base64');
}

export function createPageInfo(
  items: any[],
  totalCount: number,
  skip: number,
  take: number
) {
  const hasNextPage = skip + items.length < totalCount;
  const hasPreviousPage = skip > 0;
  
  const startCursor = items.length > 0 ? createCursor(items[0].id, skip) : null;
  const endCursor = items.length > 0 ? createCursor(items[items.length - 1].id, skip + items.length - 1) : null;
  
  return {
    hasNextPage,
    hasPreviousPage,
    startCursor,
    endCursor,
    totalCount,
  };
}
