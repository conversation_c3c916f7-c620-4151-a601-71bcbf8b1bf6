import { builder } from '../../lib/builder'
import { SortDirection, PaginationInput, PaginationInfoType, calculatePagination, createPaginationInfo } from './common'

// OrderStatus Enum
export const OrderStatusEnum = builder.enumType('OrderStatus', {
  values: {
    PENDING: { value: 'pending' },
    SHIPPED: { value: 'shipped' },
    DELIVERED: { value: 'delivered' },
    CANCELLED: { value: 'cancelled' },
  } as const,
})

// Order Object Type
export const OrderType = builder.prismaObject('Order', {
  fields: (t) => ({
    id: t.exposeID('id'),
    customerId: t.exposeString('customerId'),
    orderDate: t.expose('orderDate', { type: 'DateTime' }),
    totalAmount: t.exposeFloat('totalAmount'),
    status: t.field({
      type: OrderStatusEnum,
      resolve: (order) => order.status,
    }),
    customer: t.relation('customer'),
    items: t.relatedConnection('items', {
      cursor: 'id',
      totalCount: true,
    }),
    // Virtual field for order summary
    orderSummary: t.field({
      type: OrderSummaryType,
      resolve: async (order) => {
        const items = await builder.prisma.orderItem.findMany({
          where: { orderId: order.id },
        })

        const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
        const uniqueProducts = new Set(items.map(item => item.productVariantId)).size

        return {
          totalItems,
          uniqueProducts,
          status: order.status,
          totalAmount: Number(order.totalAmount),
        }
      },
    }),
  }),
})

// OrderItem Object Type
export const OrderItemType = builder.prismaObject('OrderItem', {
  fields: (t) => ({
    id: t.exposeID('id'),
    orderId: t.exposeString('orderId'),
    productVariantId: t.exposeString('productVariantId'),
    quantity: t.exposeInt('quantity'),
    priceAtPurchase: t.exposeFloat('priceAtPurchase'),
    order: t.relation('order'),
    productVariant: t.relation('productVariant'),
    // Virtual field for line total
    lineTotal: t.field({
      type: 'Float',
      resolve: (item) => item.priceAtPurchase * item.quantity,
    }),
  }),
})

// Order Summary Type
export const OrderSummaryType = builder.simpleObject('OrderSummary', {
  fields: (t) => ({
    totalItems: t.int(),
    uniqueProducts: t.int(),
    status: t.field({ type: OrderStatusEnum }),
    totalAmount: t.float(),
  }),
})

// Order Input Types
export const CreateOrderInput = builder.inputType('CreateOrderInput', {
  fields: (t) => ({
    customerId: t.string({ required: true }),
    items: t.field({
      type: [CreateOrderItemInput],
      required: true,
    }),
  }),
})

export const CreateOrderItemInput = builder.inputType('CreateOrderItemInput', {
  fields: (t) => ({
    productVariantId: t.string({ required: true }),
    quantity: t.int({ required: true }),
  }),
})

export const UpdateOrderStatusInput = builder.inputType('UpdateOrderStatusInput', {
  fields: (t) => ({
    id: t.string({ required: true }),
    status: t.field({ type: OrderStatusEnum, required: true }),
  }),
})

// Enhanced Order Filter Input with comprehensive options
export const OrderFilterInput = builder.inputType('OrderFilterInput', {
  fields: (t) => ({
    // Basic filters
    id: t.string({ required: false }),
    customerId: t.string({ required: false }),
    status: t.field({ type: OrderStatusEnum, required: false }),
    statuses: t.field({ type: [OrderStatusEnum], required: false, description: 'Filter by multiple statuses' }),
    
    // Date filters
    orderDate: t.field({ type: 'DateTime', required: false, description: 'Exact order date' }),
    orderDateFrom: t.field({ type: 'DateTime', required: false, description: 'Orders placed after this date' }),
    orderDateTo: t.field({ type: 'DateTime', required: false, description: 'Orders placed before this date' }),
    
    // Amount filters
    totalAmount: t.float({ required: false, description: 'Exact total amount' }),
    minAmount: t.float({ required: false, description: 'Minimum total amount' }),
    maxAmount: t.float({ required: false, description: 'Maximum total amount' }),
    
    // Customer filters
    customerEmail: t.string({ required: false, description: 'Filter by customer email' }),
    customerEmailContains: t.string({ required: false, description: 'Filter by customer email containing text' }),
    customerName: t.string({ required: false, description: 'Filter by customer name' }),
    customerNameContains: t.string({ required: false, description: 'Filter by customer name containing text' }),
    
    // Item filters
    hasProductVariant: t.string({ required: false, description: 'Orders containing this product variant' }),
    hasProduct: t.string({ required: false, description: 'Orders containing variants of this product' }),
    minItems: t.int({ required: false, description: 'Minimum number of items in order' }),
    maxItems: t.int({ required: false, description: 'Maximum number of items in order' }),
    
    // Advanced filters
    hasDiscount: t.boolean({ required: false, description: 'Orders with discount applied' }),
    discountCode: t.string({ required: false, description: 'Filter by specific discount code' }),
  }),
})

export const OrderSortInput = builder.inputType('OrderSortInput', {
  fields: (t) => ({
    field: t.field({ type: OrderSortField, required: true }),
    direction: t.field({ type: SortDirection, required: true }),
  }),
})

export const OrderSortField = builder.enumType('OrderSortField', {
  values: {
    ORDER_DATE: { value: 'orderDate' },
    TOTAL_AMOUNT: { value: 'totalAmount' },
    STATUS: { value: 'status' },
  } as const,
})

// Order List Response Type for traditional pagination
export const OrderListResponse = builder.simpleObject('OrderListResponse', {
  fields: (t) => ({
    orders: t.field({ type: [OrderType] }),
    pageInfo: t.field({ type: PaginationInfoType }),
  }),
})

// Order Queries
builder.queryField('order', (t) =>
  t.prismaField({
    type: 'Order',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      builder.prisma.order.findUnique({
        ...query,
        where: { id: args.id },
      }),
  })
)

builder.queryField('orders', (t) =>
  t.prismaConnection({
    type: 'Order',
    cursor: 'id',
    totalCount: true,
    args: {
      filter: t.arg({ type: OrderFilterInput, required: false }),
      sort: t.arg({ type: OrderSortInput, required: false }),
    },
    resolve: (query, _parent, args) => {
      const where: any = {}

      if (args.filter) {
        if (args.filter.customerId) where.customerId = args.filter.customerId
        if (args.filter.status) where.status = args.filter.status
        if (args.filter.dateFrom || args.filter.dateTo) {
          where.orderDate = {
            ...(args.filter.dateFrom && { gte: args.filter.dateFrom }),
            ...(args.filter.dateTo && { lte: args.filter.dateTo }),
          }
        }
        if (args.filter.minAmount !== undefined || args.filter.maxAmount !== undefined) {
          where.totalAmount = {
            ...(args.filter.minAmount !== undefined && { gte: args.filter.minAmount }),
            ...(args.filter.maxAmount !== undefined && { lte: args.filter.maxAmount }),
          }
        }
      }

      const orderBy: any = args.sort
        ? { [args.sort.field]: args.sort.direction }
        : { orderDate: 'desc' }

      return builder.prisma.order.findMany({
        ...query,
        where,
        orderBy,
      })
    },
  })
)

builder.queryField('orderItem', (t) =>
  t.prismaField({
    type: 'OrderItem',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      builder.prisma.orderItem.findUnique({
        ...query,
        where: { id: args.id },
      }),
  })
)

// Order Mutations
builder.mutationField('createOrder', (t) =>
  t.prismaField({
    type: 'Order',
    args: {
      input: t.arg({ type: CreateOrderInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      // Validate customer exists
      const customer = await builder.prisma.customer.findUnique({
        where: { id: args.input.customerId },
      })

      if (!customer) {
        throw new Error('Customer not found')
      }

      // Validate all product variants exist and have sufficient stock
      const variantIds = args.input.items.map(item => item.productVariantId)
      const variants = await builder.prisma.productVariant.findMany({
        where: { id: { in: variantIds } },
      })

      if (variants.length !== variantIds.length) {
        throw new Error('One or more product variants not found')
      }

      // Check stock availability
      for (const item of args.input.items) {
        const variant = variants.find(v => v.id === item.productVariantId)
        if (!variant) {
          throw new Error(`Product variant ${item.productVariantId} not found`)
        }
        if (variant.quantity < item.quantity) {
          throw new Error(`Insufficient stock for product variant ${item.productVariantId}`)
        }
      }

      // Calculate total amount
      let totalAmount = 0
      const orderItems = []

      for (const item of args.input.items) {
        const variant = variants.find(v => v.id === item.productVariantId)!
        const lineTotal = Number(variant.price) * item.quantity
        totalAmount += lineTotal

        orderItems.push({
          productVariantId: item.productVariantId,
          quantity: item.quantity,
          priceAtPurchase: variant.price,
        })
      }

      // Create order with items in a transaction
      const order = await builder.prisma.$transaction(async (tx) => {
        // Create the order
        const newOrder = await tx.order.create({
          ...query,
          data: {
            customerId: args.input.customerId,
            totalAmount,
            items: {
              create: orderItems,
            },
          },
        })

        // Update stock quantities
        for (const item of args.input.items) {
          await tx.productVariant.update({
            where: { id: item.productVariantId },
            data: {
              quantity: {
                decrement: item.quantity,
              },
            },
          })
        }

        return newOrder
      })

      return order
    },
  })
)

builder.mutationField('updateOrderStatus', (t) =>
  t.prismaField({
    type: 'Order',
    args: {
      input: t.arg({ type: UpdateOrderStatusInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.order.update({
        ...query,
        where: { id: args.input.id },
        data: { status: args.input.status },
      })
    },
  })
)

builder.mutationField('cancelOrder', (t) =>
  t.prismaField({
    type: 'Order',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, _parent, args) => {
      // Get order with items to restore stock
      const order = await builder.prisma.order.findUnique({
        where: { id: args.id },
        include: { items: true },
      })

      if (!order) {
        throw new Error('Order not found')
      }

      if (order.status === 'cancelled') {
        throw new Error('Order is already cancelled')
      }

      if (order.status === 'delivered') {
        throw new Error('Cannot cancel a delivered order')
      }

      // Cancel order and restore stock in transaction
      const cancelledOrder = await builder.prisma.$transaction(async (tx) => {
        // Update order status
        const updatedOrder = await tx.order.update({
          ...query,
          where: { id: args.id },
          data: { status: 'cancelled' },
        })

        // Restore stock quantities
        for (const item of order.items) {
          await tx.productVariant.update({
            where: { id: item.productVariantId },
            data: {
              quantity: {
                increment: item.quantity,
              },
            },
          })
        }

        return updatedOrder
      })

      return cancelledOrder
    },
  })
)

builder.mutationField('deleteOrder', (t) =>
  t.prismaField({
    type: 'Order',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.order.delete({
        ...query,
        where: { id: args.id },
      })
    },
  })
)
