/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { builder } from '../../lib/builder';
import { prisma } from '../../lib/prisma';
import { z } from 'zod';
import {
  PaginationInput,
  PageInfoInput,
  Pagination,
  SortInput,
  DateRangeInput,
  StringFilterInput,
  NumberRangeInput,
  calculateTraditionalPagination,
  calculateCursorPagination,
  createPageInfo,
  PaginationInputSchema,
  PageInfoInputSchema,
} from './pagination';

// OrderStatus enum
export const OrderStatus = builder.enumType('OrderStatus', {
  values: {
    PENDING: { value: 'pending' },
    SHIPPED: { value: 'shipped' },
    DELIVERED: { value: 'delivered' },
    CANCELLED: { value: 'cancelled' },
  },
});

// Order object type
export const Order = builder.prismaObject('Order', {
  fields: (t) => ({
    id: t.exposeID('id'),
    customerId: t.exposeString('customerId'),
    orderDate: t.expose('orderDate', { type: 'DateTime' }),
    totalAmount: t.exposeFloat('totalAmount'),
    status: t.expose('status', { type: OrderStatus }),
    // Relations
    customer: t.relation('customer'),
    items: t.relation('items'),
    paymentTransaction: t.relation('paymentTransaction', { nullable: true }),
    // Computed fields
    itemCount: t.int({
      resolve: async (order, args, ctx) => {
        return ctx.prisma.orderItem.count({
          where: { orderId: order.id },
        });
      },
    }),
    totalItems: t.int({
      resolve: async (order, _, ctx) => {
        const items = await ctx.prisma.orderItem.findMany({
          where: { orderId: order.id },
          select: { quantity: true },
        });
        return items.reduce((sum, item) => sum + item.quantity, 0);
      },
    }),
  }),
});

// OrderItem object type
export const OrderItem = builder.prismaObject('OrderItem', {
  fields: (t) => ({
    id: t.exposeID('id'),
    orderId: t.exposeString('orderId'),
    productVariantId: t.exposeString('productVariantId'),
    quantity: t.exposeInt('quantity'),
    priceAtPurchase: t.exposeFloat('priceAtPurchase'),
    // Relations
    order: t.relation('order'),
    productVariant: t.relation('productVariant'),
    // Computed fields
    totalPrice: t.float({
      resolve: (orderItem) => orderItem.quantity * orderItem.priceAtPurchase,
    }),
  }),
});

// PaymentMethod object type
export const PaymentMethod = builder.prismaObject('PaymentMethod', {
  fields: (t) => ({
    id: t.exposeID('id'),
    customerId: t.exposeString('customerId'),
    provider: t.exposeString('provider'),
    type: t.exposeString('type'),
    last4: t.exposeString('last4'),
    isDefault: t.exposeBoolean('isDefault'),
    metadata: t.expose('metadata', { type: 'JSON', nullable: true }),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    // Relations
    customer: t.relation('customer'),
    transactions: t.relation('transactions'),
  }),
});

// PaymentTransaction object type
export const PaymentTransaction = builder.prismaObject('PaymentTransaction', {
  fields: (t) => ({
    id: t.exposeID('id'),
    paymentMethodId: t.exposeString('paymentMethodId'),
    orderId: t.exposeString('orderId', { nullable: true }),
    amount: t.exposeFloat('amount'),
    status: t.exposeString('status'),
    transactionId: t.exposeString('transactionId'),
    providerResponse: t.expose('providerResponse', { type: 'JSON', nullable: true }),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    // Relations
    paymentMethod: t.relation('paymentMethod'),
    order: t.relation('order', { nullable: true }),
  }),
});

// Order filter input
const OrderFilterSchema = z.object({
  id: z.string().optional(),
  customerId: z.string().optional(),
  customerIds: z.array(z.string()).optional(),
  status: z.enum(['pending', 'shipped', 'delivered', 'cancelled']).optional(),
  statuses: z.array(z.enum(['pending', 'shipped', 'delivered', 'cancelled'])).optional(),
  orderDate: z.object({
    from: z.date().optional(),
    to: z.date().optional(),
  }).optional(),
  totalAmount: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
  }).optional(),
  hasPayment: z.boolean().optional(),
  paymentStatus: z.string().optional(),
  productId: z.string().optional(),
  categoryId: z.string().optional(),
});

export const OrderFilterInput = builder.inputType('OrderFilterInput', {
  fields: (t) => ({
    id: t.string({ description: 'Filter by order ID' }),
    customerId: t.string({ description: 'Filter by customer ID' }),
    customerIds: t.stringList({ description: 'Filter by multiple customer IDs' }),
    status: t.field({ type: OrderStatus, description: 'Filter by order status' }),
    statuses: t.field({ type: [OrderStatus], description: 'Filter by multiple order statuses' }),
    orderDate: t.field({ type: DateRangeInput, description: 'Filter by order date range' }),
    totalAmount: t.field({ type: NumberRangeInput, description: 'Filter by total amount range' }),
    hasPayment: t.boolean({ description: 'Filter orders with/without payment' }),
    paymentStatus: t.string({ description: 'Filter by payment status' }),
    productId: t.string({ description: 'Filter orders containing specific product' }),
    categoryId: t.string({ description: 'Filter orders containing products from category' }),
  }),
});

// Order sort fields enum
export const OrderSortField = builder.enumType('OrderSortField', {
  values: {
    ID: { value: 'id' },
    ORDER_DATE: { value: 'orderDate' },
    TOTAL_AMOUNT: { value: 'totalAmount' },
    STATUS: { value: 'status' },
  },
});

// Order connection type for cursor-based pagination
export const OrderConnection = builder.connectionObject({
  type: Order,
  name: 'OrderConnection',
});

// Order list with traditional pagination
export const OrderList = builder.objectType('OrderList', {
  fields: (t) => ({
    items: t.field({ type: [Order], description: 'List of orders' }),
    pagination: t.field({ type: Pagination, description: 'Pagination information' }),
  }),
});

// Helper function to build Prisma where clause for orders
function buildOrderWhereClause(filter: any) {
  const where: any = {};

  if (filter?.id) {
    where.id = filter.id;
  }

  if (filter?.customerId) {
    where.customerId = filter.customerId;
  }

  if (filter?.customerIds?.length) {
    where.customerId = { in: filter.customerIds };
  }

  if (filter?.status) {
    where.status = filter.status;
  }

  if (filter?.statuses?.length) {
    where.status = { in: filter.statuses };
  }

  if (filter?.orderDate) {
    where.orderDate = {};
    if (filter.orderDate.from) {
      where.orderDate.gte = filter.orderDate.from;
    }
    if (filter.orderDate.to) {
      where.orderDate.lte = filter.orderDate.to;
    }
  }

  if (filter?.totalAmount) {
    where.totalAmount = {};
    if (filter.totalAmount.min) {
      where.totalAmount.gte = filter.totalAmount.min;
    }
    if (filter.totalAmount.max) {
      where.totalAmount.lte = filter.totalAmount.max;
    }
  }

  if (filter?.hasPayment !== undefined) {
    if (filter.hasPayment) {
      where.paymentTransaction = { isNot: null };
    } else {
      where.paymentTransaction = null;
    }
  }

  if (filter?.paymentStatus) {
    where.paymentTransaction = {
      status: filter.paymentStatus,
    };
  }

  if (filter?.productId) {
    where.items = {
      some: {
        productVariant: {
          productId: filter.productId,
        },
      },
    };
  }

  if (filter?.categoryId) {
    where.items = {
      some: {
        productVariant: {
          product: {
            categoryId: filter.categoryId,
          },
        },
      },
    };
  }

  return where;
}

function buildOrderOrderByClause(sort?: { field: string; direction: 'asc' | 'desc' }[]) {
  if (!sort?.length) {
    return [{ orderDate: 'desc' as const }];
  }

  return sort.map(s => ({ [s.field]: s.direction }));
}

// Query fields for orders
builder.queryFields((t) => ({
  // Single order query
  order: t.prismaField({
    type: 'Order',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, root, args, ctx) => {
      return ctx.prisma.order.findUnique({
        ...query,
        where: { id: args.id },
      });
    },
  }),

  // Orders with cursor-based pagination
  orders: t.connection({
    type: Order,
    args: {
      filter: t.arg({ type: OrderFilterInput }),
      sort: t.arg({ type: [SortInput] }),
    },
    resolve: async (root, args, ctx) => {
      const filter = args.filter;
      const sort = args.sort;
      
      const where = buildOrderWhereClause(filter);
      const orderBy = buildOrderOrderByClause(sort);

      // Get total count
      const totalCount = await ctx.prisma.order.count({ where });

      // Calculate pagination
      const { skip, take } = calculateCursorPagination(args, totalCount);

      // Get orders
      const orders = await ctx.prisma.order.findMany({
        where,
        orderBy,
        skip,
        take,
      });

      // Create page info
      const pageInfo = createPageInfo(orders, totalCount, skip, take);

      return {
        edges: orders.map((order, index) => ({
          node: order,
          cursor: pageInfo.startCursor || '',
        })),
        pageInfo,
      };
    },
  }),

  // Orders with traditional pagination
  ordersList: t.field({
    type: OrderList,
    args: {
      pagination: t.arg({ type: PaginationInput }),
      filter: t.arg({ type: OrderFilterInput }),
      sort: t.arg({ type: [SortInput] }),
    },
    validate: {
      schema: z.object({
        pagination: PaginationInputSchema.optional(),
        filter: OrderFilterSchema.optional(),
        sort: z.array(z.object({
          field: z.string(),
          direction: z.enum(['asc', 'desc']),
        })).optional(),
      }),
    },
    resolve: async (root, args, ctx) => {
      const { pagination = {}, filter, sort } = args;
      
      const where = buildOrderWhereClause(filter);
      const orderBy = buildOrderOrderByClause(sort);

      // Get total count
      const totalCount = await ctx.prisma.order.count({ where });

      // Calculate pagination
      const { skip, take, pagination: paginationInfo } = calculateTraditionalPagination(
        pagination.page || 1,
        pagination.pageSize || 10,
        totalCount,
        pagination.offset,
        pagination.skip
      );

      // Get orders
      const orders = await ctx.prisma.order.findMany({
        where,
        orderBy,
        skip,
        take,
      });

      return {
        items: orders,
        pagination: paginationInfo,
      };
    },
  }),
}));
