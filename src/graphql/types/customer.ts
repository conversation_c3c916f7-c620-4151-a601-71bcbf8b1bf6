import { builder } from '../../lib/builder';
import { z } from 'zod';
import {
  PaginationInput,
  Pagination,
  SortInput,
  DateRangeInput,
  StringFilterInput,
  calculateTraditionalPagination,
  calculateCursorPagination,
  createPageInfo,
  PaginationInputSchema,
} from './pagination';

// Customer object type
export const Customer = builder.prismaObject('Customer', {
  fields: (t) => ({
    id: t.exposeID('id'),
    email: t.exposeString('email'),
    firstName: t.exposeString('firstName'),
    lastName: t.exposeString('lastName'),
    phone: t.exposeString('phone', { nullable: true }),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    fullName: t.string({
      resolve: (customer) => `${customer.firstName} ${customer.lastName}`,
    }),
    // Relations
    wishlist: t.relation('wishlist', { nullable: true }),
    reviews: t.relation('reviews'),
    orders: t.relation('orders'),
    paymentMethods: t.relation('paymentMethods'),
    promoCodeUsages: t.relation('promoCodeUsages'),
  }),
});

// Customer filter input
export const CustomerFilterInput = builder.inputType('CustomerFilterInput', {
  fields: (t) => ({
    id: t.string({ description: 'Filter by customer ID' }),
    email: t.field({ type: StringFilterInput, description: 'Filter by email' }),
    firstName: t.field({ type: StringFilterInput, description: 'Filter by first name' }),
    lastName: t.field({ type: StringFilterInput, description: 'Filter by last name' }),
    phone: t.field({ type: StringFilterInput, description: 'Filter by phone number' }),
    createdAt: t.field({ type: DateRangeInput, description: 'Filter by creation date range' }),
    hasWishlist: t.boolean({ description: 'Filter customers with/without wishlist' }),
    hasOrders: t.boolean({ description: 'Filter customers with/without orders' }),
    hasReviews: t.boolean({ description: 'Filter customers with/without reviews' }),
    search: t.string({ description: 'Global search across name and email' }),
  }),
});

// Customer sort fields enum
export const CustomerSortField = builder.enumType('CustomerSortField', {
  values: {
    ID: { value: 'id' },
    EMAIL: { value: 'email' },
    FIRST_NAME: { value: 'firstName' },
    LAST_NAME: { value: 'lastName' },
    CREATED_AT: { value: 'createdAt' },
  },
});

// Customer connection type for cursor-based pagination
export const CustomerConnection = builder.connectionObject({
  type: Customer,
  name: 'CustomerConnection',
});

// Helper functions
function buildCustomerWhereClause(filter: any) {
  const where: any = {};

  if (filter?.id) {
    where.id = filter.id;
  }

  if (filter?.email) {
    where.email = buildStringFilter(filter.email);
  }

  if (filter?.firstName) {
    where.firstName = buildStringFilter(filter.firstName);
  }

  if (filter?.lastName) {
    where.lastName = buildStringFilter(filter.lastName);
  }

  if (filter?.phone) {
    where.phone = buildStringFilter(filter.phone);
  }

  if (filter?.createdAt) {
    where.createdAt = {};
    if (filter.createdAt.from) {
      where.createdAt.gte = filter.createdAt.from;
    }
    if (filter.createdAt.to) {
      where.createdAt.lte = filter.createdAt.to;
    }
  }

  if (filter?.hasWishlist !== undefined) {
    if (filter.hasWishlist) {
      where.wishlist = { isNot: null };
    } else {
      where.wishlist = null;
    }
  }

  if (filter?.hasOrders !== undefined) {
    if (filter.hasOrders) {
      where.orders = { some: {} };
    } else {
      where.orders = { none: {} };
    }
  }

  if (filter?.hasReviews !== undefined) {
    if (filter.hasReviews) {
      where.reviews = { some: {} };
    } else {
      where.reviews = { none: {} };
    }
  }

  if (filter?.search) {
    where.OR = [
      { firstName: { contains: filter.search, mode: 'insensitive' } },
      { lastName: { contains: filter.search, mode: 'insensitive' } },
      { email: { contains: filter.search, mode: 'insensitive' } },
    ];
  }

  return where;
}

function buildStringFilter(filter: any) {
  const result: any = {};

  if (filter.equals) result.equals = filter.equals;
  if (filter.contains) result.contains = filter.contains;
  if (filter.startsWith) result.startsWith = filter.startsWith;
  if (filter.endsWith) result.endsWith = filter.endsWith;
  if (filter.in) result.in = filter.in;
  if (filter.notIn) result.notIn = filter.notIn;

  if (filter.contains || filter.startsWith || filter.endsWith) {
    result.mode = 'insensitive';
  }

  return result;
}

function buildOrderByClause(sort?: { field: string; direction?: 'asc' | 'desc' | null }[] | null) {
  if (!sort?.length) {
    return [{ createdAt: 'desc' as const }];
  }

  return sort
    .filter(s => s.direction && (s.direction === 'asc' || s.direction === 'desc'))
    .map(s => ({ [s.field]: s.direction as 'asc' | 'desc' }));
}

// Query fields
builder.queryFields((t) => ({
  // Single customer query
  customer: t.prismaField({
    type: 'Customer',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, root, args, ctx) => {
      return ctx.prisma.customer.findUnique({
        ...query,
        where: { id: args.id },
      });
    },
  }),

  // Customers with cursor-based pagination
  customers: t.connection({
    type: Customer,
    args: {
      filter: t.arg({ type: CustomerFilterInput }),
      sort: t.arg({ type: [SortInput] }),
    },
    resolve: async (root, args, ctx) => {
      const filter = args.filter;
      const sort = args.sort;
      
      const where = buildCustomerWhereClause(filter);
      const orderBy = buildOrderByClause(sort);

      // Get total count
      const totalCount = await ctx.prisma.customer.count({ where });

      // Calculate pagination
      const { skip, take } = calculateCursorPagination(args, totalCount);

      // Get customers
      const customers = await ctx.prisma.customer.findMany({
        where,
        orderBy,
        skip,
        take,
      });

      // Create page info
      const pageInfo = createPageInfo(customers, totalCount, skip, take);

      return {
        edges: customers.map((customer) => ({
          node: customer,
          cursor: customer.id, // Use ID as cursor for simplicity
        })),
        pageInfo,
      };
    },
  }),
}));
