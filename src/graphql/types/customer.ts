import { builder } from '../../lib/builder'

// Customer GraphQL type
export const Customer = builder.prismaObject('Customer', {
  fields: (t) => ({
    id: t.exposeID('id'),
    email: t.exposeString('email'),
    firstName: t.exposeString('firstName'),
    lastName: t.exposeString('lastName'),
    phone: t.exposeString('phone', { nullable: true }),
    dateOfBirth: t.expose('dateOfBirth', { type: 'DateTime', nullable: true }),
    gender: t.expose('gender', { type: 'Gender', nullable: true }),
    isActive: t.exposeBoolean('isActive'),
    isVerified: t.exposeBoolean('isVerified'),
    lastLoginAt: t.expose('lastLoginAt', { type: 'DateTime', nullable: true }),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),

    // Relations
    addresses: t.relation('addresses'),
    orders: t.relation('orders'),
    reviews: t.relation('reviews'),
    cart: t.relation('cart', { nullable: true }),
    wishlist: t.relation('wishlist', { nullable: true }),

    // Computed fields
    fullName: t.string({
      resolve: (customer) => `${customer.firstName} ${customer.lastName}`
    }),

    orderCount: t.int({
      resolve: async (customer, args, { prisma }) => {
        return prisma.order.count({
          where: { customerId: customer.id }
        })
      }
    }),
  }),
})