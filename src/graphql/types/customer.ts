import { builder } from '../../lib/builder'

// Customer Object Type
export const CustomerType = builder.prismaObject('Customer', {
  fields: (t) => ({
    id: t.exposeID('id'),
    email: t.exposeString('email'),
    firstName: t.exposeString('firstName'),
    lastName: t.exposeString('lastName'),
    phone: t.exposeString('phone', { nullable: true }),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    orders: t.relatedConnection('orders', {
      cursor: 'id',
      totalCount: true,
    }),
    // Virtual field for full name
    fullName: t.field({
      type: 'String',
      resolve: (customer) => `${customer.firstName} ${customer.lastName}`,
    }),
    // Virtual field for order statistics
    orderStats: t.field({
      type: CustomerOrderStatsType,
      resolve: async (customer) => {
        const orders = await builder.prisma.order.findMany({
          where: { customerId: customer.id },
          include: { items: true },
        })

        const totalOrders = orders.length
        const totalSpent = orders.reduce((sum, order) => sum + Number(order.totalAmount), 0)
        const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0
        const totalItems = orders.reduce((sum, order) => sum + order.items.length, 0)

        return {
          totalOrders,
          totalSpent,
          averageOrderValue,
          totalItems,
        }
      },
    }),
  }),
})

// Customer Order Stats Type
export const CustomerOrderStatsType = builder.simpleObject('CustomerOrderStats', {
  fields: (t) => ({
    totalOrders: t.int(),
    totalSpent: t.float(),
    averageOrderValue: t.float(),
    totalItems: t.int(),
  }),
})

// Customer Input Types
export const CreateCustomerInput = builder.inputType('CreateCustomerInput', {
  fields: (t) => ({
    email: t.string({ required: true }),
    firstName: t.string({ required: true }),
    lastName: t.string({ required: true }),
    phone: t.string({ required: false }),
  }),
})

export const UpdateCustomerInput = builder.inputType('UpdateCustomerInput', {
  fields: (t) => ({
    id: t.string({ required: true }),
    email: t.string({ required: false }),
    firstName: t.string({ required: false }),
    lastName: t.string({ required: false }),
    phone: t.string({ required: false }),
  }),
})

export const CustomerFilterInput = builder.inputType('CustomerFilterInput', {
  fields: (t) => ({
    email: t.string({ required: false }),
    name: t.string({ required: false }),
    hasOrders: t.boolean({ required: false }),
  }),
})

// Customer Queries
builder.queryField('customer', (t) =>
  t.prismaField({
    type: 'Customer',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      builder.prisma.customer.findUnique({
        ...query,
        where: { id: args.id },
      }),
  })
)

builder.queryField('customerByEmail', (t) =>
  t.prismaField({
    type: 'Customer',
    nullable: true,
    args: {
      email: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      builder.prisma.customer.findUnique({
        ...query,
        where: { email: args.email },
      }),
  })
)

builder.queryField('customers', (t) =>
  t.prismaConnection({
    type: 'Customer',
    cursor: 'id',
    totalCount: true,
    args: {
      filter: t.arg({ type: CustomerFilterInput, required: false }),
    },
    resolve: (query, _parent, args) => {
      const where: any = {}

      if (args.filter) {
        if (args.filter.email) {
          where.email = {
            contains: args.filter.email,
            mode: 'insensitive',
          }
        }
        if (args.filter.name) {
          where.OR = [
            {
              firstName: {
                contains: args.filter.name,
                mode: 'insensitive',
              },
            },
            {
              lastName: {
                contains: args.filter.name,
                mode: 'insensitive',
              },
            },
          ]
        }
        if (args.filter.hasOrders !== undefined) {
          if (args.filter.hasOrders) {
            where.orders = {
              some: {},
            }
          } else {
            where.orders = {
              none: {},
            }
          }
        }
      }

      return builder.prisma.customer.findMany({
        ...query,
        where,
        orderBy: { createdAt: 'desc' },
      })
    },
  })
)

// Customer Mutations
builder.mutationField('createCustomer', (t) =>
  t.prismaField({
    type: 'Customer',
    args: {
      input: t.arg({ type: CreateCustomerInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      // Check if customer with email already exists
      const existingCustomer = await builder.prisma.customer.findUnique({
        where: { email: args.input.email },
      })

      if (existingCustomer) {
        throw new Error('Customer with this email already exists')
      }

      return builder.prisma.customer.create({
        ...query,
        data: {
          email: args.input.email,
          firstName: args.input.firstName,
          lastName: args.input.lastName,
          phone: args.input.phone,
        },
      })
    },
  })
)

builder.mutationField('updateCustomer', (t) =>
  t.prismaField({
    type: 'Customer',
    args: {
      input: t.arg({ type: UpdateCustomerInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      const updateData: any = {}

      if (args.input.email) {
        // Check if another customer with this email exists
        const existingCustomer = await builder.prisma.customer.findUnique({
          where: { email: args.input.email },
        })

        if (existingCustomer && existingCustomer.id !== args.input.id) {
          throw new Error('Another customer with this email already exists')
        }

        updateData.email = args.input.email
      }

      if (args.input.firstName) updateData.firstName = args.input.firstName
      if (args.input.lastName) updateData.lastName = args.input.lastName
      if (args.input.phone !== undefined) updateData.phone = args.input.phone

      return builder.prisma.customer.update({
        ...query,
        where: { id: args.input.id },
        data: updateData,
      })
    },
  })
)

builder.mutationField('deleteCustomer', (t) =>
  t.prismaField({
    type: 'Customer',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.customer.delete({
        ...query,
        where: { id: args.id },
      })
    },
  })
)
