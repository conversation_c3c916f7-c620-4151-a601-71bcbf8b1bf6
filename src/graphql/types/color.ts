import { builder } from '../../lib/builder'

// Color Object Type
export const ColorType = builder.prismaObject('Color', {
  fields: (t) => ({
    id: t.exposeID('id'),
    colorName: t.exposeString('colorName'),
    hexCode: t.exposeString('hexCode', { nullable: true }),
    variants: t.relatedConnection('variants', {
      cursor: 'id',
      totalCount: true,
    }),
  }),
})

// Color Input Types
export const CreateColorInput = builder.inputType('CreateColorInput', {
  fields: (t) => ({
    colorName: t.string({ required: true }),
    hexCode: t.string({ required: false }),
  }),
})

export const UpdateColorInput = builder.inputType('UpdateColorInput', {
  fields: (t) => ({
    id: t.string({ required: true }),
    colorName: t.string({ required: false }),
    hexCode: t.string({ required: false }),
  }),
})

// Color Queries
builder.queryField('color', (t) =>
  t.prismaField({
    type: 'Color',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      builder.prisma.color.findUnique({
        ...query,
        where: { id: args.id },
      }),
  })
)

builder.queryField('colors', (t) =>
  t.prismaConnection({
    type: 'Color',
    cursor: 'id',
    totalCount: true,
    resolve: (query) =>
      builder.prisma.color.findMany({
        ...query,
        orderBy: { colorName: 'asc' },
      }),
  })
)

// Color Mutations
builder.mutationField('createColor', (t) =>
  t.prismaField({
    type: 'Color',
    args: {
      input: t.arg({ type: CreateColorInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.color.create({
        ...query,
        data: {
          colorName: args.input.colorName,
          hexCode: args.input.hexCode,
        },
      })
    },
  })
)

builder.mutationField('updateColor', (t) =>
  t.prismaField({
    type: 'Color',
    args: {
      input: t.arg({ type: UpdateColorInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      const updateData: any = {}
      if (args.input.colorName) updateData.colorName = args.input.colorName
      if (args.input.hexCode !== undefined) updateData.hexCode = args.input.hexCode

      return builder.prisma.color.update({
        ...query,
        where: { id: args.input.id },
        data: updateData,
      })
    },
  })
)

builder.mutationField('deleteColor', (t) =>
  t.prismaField({
    type: 'Color',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, _parent, args) => {
      return builder.prisma.color.delete({
        ...query,
        where: { id: args.id },
      })
    },
  })
)
