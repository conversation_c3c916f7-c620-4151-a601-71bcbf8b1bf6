import { builder } from '../../lib/builder'
import { createConnection } from '../../lib/pagination'

// Order direction enum
export const OrderDirection = builder.enumType('OrderDirection', {
  values: {
    ASC: { value: 'asc' },
    DESC: { value: 'desc' },
  },
})

// Filter input types
export const StringFilter = builder.inputType('StringFilter', {
  fields: (t) => ({
    equals: t.string({ required: false }),
    in: t.stringList({ required: false }),
    notIn: t.stringList({ required: false }),
    contains: t.string({ required: false }),
    startsWith: t.string({ required: false }),
    endsWith: t.string({ required: false }),
    mode: t.field({ type: 'String', required: false }),
  }),
})

export const BooleanFilter = builder.inputType('BooleanFilter', {
  fields: (t) => ({
    equals: t.boolean({ required: false }),
  }),
})

export const FloatFilter = builder.inputType('FloatFilter', {
  fields: (t) => ({
    equals: t.float({ required: false }),
    gt: t.float({ required: false }),
    gte: t.float({ required: false }),
    lt: t.float({ required: false }),
    lte: t.float({ required: false }),
  }),
})

export const DateTimeFilter = builder.inputType('DateTimeFilter', {
  fields: (t) => ({
    equals: t.field({ type: 'DateTime', required: false }),
    gt: t.field({ type: 'DateTime', required: false }),
    gte: t.field({ type: 'DateTime', required: false }),
    lt: t.field({ type: 'DateTime', required: false }),
    lte: t.field({ type: 'DateTime', required: false }),
  }),
})

export const StringListFilter = builder.inputType('StringListFilter', {
  fields: (t) => ({
    has: t.string({ required: false }),
    hasEvery: t.stringList({ required: false }),
    hasSome: t.stringList({ required: false }),
    isEmpty: t.boolean({ required: false }),
  }),
})

// Product GraphQL type
export const Product = builder.prismaObject('Product', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    description: t.exposeString('description'),
    slug: t.exposeString('slug'),
    sku: t.exposeString('sku', { nullable: true }),
    brand: t.exposeString('brand', { nullable: true }),
    tags: t.exposeStringList('tags'),
    isActive: t.exposeBoolean('isActive'),
    isFeatured: t.exposeBoolean('isFeatured'),
    weight: t.exposeFloat('weight', { nullable: true }),
    dimensions: t.expose('dimensions', { type: 'JSON', nullable: true }),
    material: t.exposeString('material', { nullable: true }),
    careInstructions: t.exposeString('careInstructions', { nullable: true }),
    categoryId: t.exposeString('categoryId'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),

    // Relations
    category: t.relation('category'),
    variants: t.relation('variants'),
    images: t.relation('images'),
    reviews: t.relation('reviews'),
    wishlistItems: t.relation('wishlistItems'),

    // Computed fields
    variantCount: t.int({
      resolve: async (product, args, { prisma }) => {
        return prisma.productVariant.count({
          where: { productId: product.id }
        })
      }
    }),

    averageRating: t.float({
      nullable: true,
      resolve: async (product, args, { prisma }) => {
        const result = await prisma.productReview.aggregate({
          where: {
            productId: product.id,
            isApproved: true
          },
          _avg: { rating: true }
        })
        return result._avg.rating
      }
    }),

    minPrice: t.float({
      nullable: true,
      resolve: async (product, args, { prisma }) => {
        const result = await prisma.productVariant.aggregate({
          where: {
            productId: product.id,
            isActive: true
          },
          _min: { price: true }
        })
        return result._min.price
      }
    }),

    totalStock: t.int({
      resolve: async (product, args, { prisma }) => {
        const result = await prisma.productVariant.aggregate({
          where: {
            productId: product.id,
            isActive: true
          },
          _sum: { quantity: true }
        })
        return result._sum.quantity || 0
      }
    }),
  }),
})

// Simple product filter input
export const ProductWhereInput = builder.inputType('ProductWhereInput', {
  fields: (t) => ({
    isActive: t.boolean({ required: false }),
    isFeatured: t.boolean({ required: false }),
    categoryId: t.string({ required: false }),
    brand: t.string({ required: false }),
    search: t.string({ required: false }),
    minPrice: t.float({ required: false }),
    maxPrice: t.float({ required: false }),
  }),
})

export const ProductOrderByInput = builder.inputType('ProductOrderByInput', {
  fields: (t) => ({
    name: t.string({ required: false }),
    createdAt: t.string({ required: false }),
    isFeatured: t.string({ required: false }),
  }),
})

// Create connection types
export const { Connection: ProductConnection, Edge: ProductEdge } = createConnection('Product', Product)

// Product create input
export const ProductCreateInput = builder.inputType('ProductCreateInput', {
  fields: (t) => ({
    name: t.string({ required: true }),
    description: t.string({ required: true }),
    slug: t.string({ required: true }),
    sku: t.string({ required: false }),
    brand: t.string({ required: false }),
    tags: t.stringList({ required: false }),
    isActive: t.boolean({ required: false }),
    isFeatured: t.boolean({ required: false }),
    weight: t.float({ required: false }),
    dimensions: t.field({ type: 'JSON', required: false }),
    material: t.string({ required: false }),
    careInstructions: t.string({ required: false }),
    categoryId: t.string({ required: true }),
  }),
})