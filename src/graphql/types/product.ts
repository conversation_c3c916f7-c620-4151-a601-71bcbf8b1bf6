import { builder } from '../../lib/builder'
import { prisma } from '../../lib/prisma'
import { SortDirection, PaginationInput, PageInfoType, calculatePagination, createPageInfo } from './common'

// Product Object Type
export const ProductType = builder.prismaObject('Product', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    description: t.exposeString('description'),
    categoryId: t.exposeString('categoryId'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    category: t.relation('category'),
    variants: t.relatedConnection('variants', {
      cursor: 'id',
      totalCount: true,
    }),
    images: t.relatedConnection('images', {
      cursor: 'id',
      totalCount: true,
    }),
  }),
})

// Enhanced Product Filter Input with comprehensive options
export const ProductFilterInput = builder.inputType('ProductFilterInput', {
  fields: (t) => ({
    // Basic filters
    id: t.string({ required: false }),
    name: t.string({ required: false }),
    nameContains: t.string({ required: false, description: 'Search by name containing text (case insensitive)' }),
    description: t.string({ required: false }),
    descriptionContains: t.string({ required: false, description: 'Search by description containing text (case insensitive)' }),
    categoryId: t.string({ required: false }),
    
    // Price range filters
    minPrice: t.float({ required: false, description: 'Minimum price across all variants' }),
    maxPrice: t.float({ required: false, description: 'Maximum price across all variants' }),
    
    // Inventory filters
    inStock: t.boolean({ required: false, description: 'Filter products that have variants in stock' }),
    outOfStock: t.boolean({ required: false, description: 'Filter products that are out of stock' }),
    
    // Category filters
    categoryName: t.string({ required: false, description: 'Filter by exact category name' }),
    categoryNameContains: t.string({ required: false, description: 'Filter by category name containing text' }),
    
    // Size and color filters
    hasSize: t.string({ required: false, description: 'Filter products that have this exact size' }),
    hasColor: t.string({ required: false, description: 'Filter products that have this exact color' }),
    hasSizes: t.stringList({ required: false, description: 'Filter products that have any of these sizes' }),
    hasColors: t.stringList({ required: false, description: 'Filter products that have any of these colors' }),
    
    // Variant filters
    hasVariants: t.boolean({ required: false, description: 'Filter products that have variants' }),
    minVariants: t.int({ required: false, description: 'Minimum number of variants' }),
    maxVariants: t.int({ required: false, description: 'Maximum number of variants' }),
    
    // Image filters
    hasImages: t.boolean({ required: false, description: 'Filter products that have images' }),
    hasPrimaryImage: t.boolean({ required: false, description: 'Filter products that have a primary image' }),
    
    // Date filters
    createdAfter: t.field({ type: 'DateTime', required: false, description: 'Created after this date' }),
    createdBefore: t.field({ type: 'DateTime', required: false, description: 'Created before this date' }),
    updatedAfter: t.field({ type: 'DateTime', required: false, description: 'Updated after this date' }),
    updatedBefore: t.field({ type: 'DateTime', required: false, description: 'Updated before this date' }),
  }),
})

// Product Sort Input
export const ProductSortInput = builder.inputType('ProductSortInput', {
  fields: (t) => ({
    field: t.field({ type: ProductSortField, required: true }),
    direction: t.field({ type: SortDirection, required: true }),
  }),
})

export const ProductSortField = builder.enumType('ProductSortField', {
  values: {
    name: { description: 'Sort by product name' },
    createdAt: { description: 'Sort by creation date' },
    updatedAt: { description: 'Sort by last update date' },
    price: { description: 'Sort by minimum variant price' },
  },
})

// Product Input Types
export const CreateProductInput = builder.inputType('CreateProductInput', {
  fields: (t) => ({
    name: t.string({ required: true }),
    description: t.string({ required: true }),
    categoryId: t.string({ required: true }),
  }),
})

export const UpdateProductInput = builder.inputType('UpdateProductInput', {
  fields: (t) => ({
    id: t.string({ required: true }),
    name: t.string({ required: false }),
    description: t.string({ required: false }),
    categoryId: t.string({ required: false }),
  }),
})

// Product List Response Type for traditional pagination
export const ProductListResponse = builder.simpleObject('ProductListResponse', {
  fields: (t) => ({
    products: t.field({ type: [ProductType] }),
    pageInfo: t.field({ type: PageInfoType }),
  }),
})

// Product Queries
builder.queryField('product', (t) =>
  t.prismaField({
    type: 'Product',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: (query, _parent, args) =>
      prisma.product.findUnique({
        where: { id: args.id },
      }),
  })
)

// Traditional pagination products query
builder.queryField('products', (t) =>
  t.field({
    type: ProductListResponse,
    args: {
      pagination: t.arg({ type: PaginationInput, required: false }),
      filter: t.arg({ type: ProductFilterInput, required: false }),
      sort: t.arg({ type: ProductSortInput, required: false }),
    },
    resolve: async (_parent, args) => {
      const { take, skip, page, pageSize } = calculatePagination(args.pagination || {})
      
      // Build where clause for filtering
      const where: any = {}
      
      if (args.filter) {
        const filter = args.filter
        
        // Basic filters
        if (filter.id) where.id = filter.id
        if (filter.name) where.name = filter.name
        if (filter.nameContains) where.name = { contains: filter.nameContains, mode: 'insensitive' }
        if (filter.description) where.description = filter.description
        if (filter.descriptionContains) where.description = { contains: filter.descriptionContains, mode: 'insensitive' }
        if (filter.categoryId) where.categoryId = filter.categoryId
        
        // Category name filters
        if (filter.categoryName || filter.categoryNameContains) {
          where.category = {}
          if (filter.categoryName) where.category.name = filter.categoryName
          if (filter.categoryNameContains) where.category.name = { contains: filter.categoryNameContains, mode: 'insensitive' }
        }
        
        // Date filters
        if (filter.createdAfter || filter.createdBefore) {
          where.createdAt = {}
          if (filter.createdAfter) where.createdAt.gte = filter.createdAfter
          if (filter.createdBefore) where.createdAt.lte = filter.createdBefore
        }
        if (filter.updatedAfter || filter.updatedBefore) {
          where.updatedAt = {}
          if (filter.updatedAfter) where.updatedAt.gte = filter.updatedAfter
          if (filter.updatedBefore) where.updatedAt.lte = filter.updatedBefore
        }
        
        // Price filters
        if (filter.minPrice !== undefined || filter.maxPrice !== undefined) {
          where.variants = { some: { price: {} } }
          if (filter.minPrice !== undefined) where.variants.some.price.gte = filter.minPrice
          if (filter.maxPrice !== undefined) where.variants.some.price.lte = filter.maxPrice
        }
        
        // Stock filters
        if (filter.inStock === true) {
          where.variants = { some: { stock: { gt: 0 } } }
        }
        if (filter.outOfStock === true) {
          where.variants = { every: { stock: 0 } }
        }
        
        // Size and color filters
        if (filter.hasSize) {
          where.variants = { some: { size: { name: filter.hasSize } } }
        }
        if (filter.hasColor) {
          where.variants = { some: { color: { name: filter.hasColor } } }
        }
        if (filter.hasSizes && filter.hasSizes.length > 0) {
          where.variants = { some: { size: { name: { in: filter.hasSizes } } } }
        }
        if (filter.hasColors && filter.hasColors.length > 0) {
          where.variants = { some: { color: { name: { in: filter.hasColors } } } }
        }
        
        // Variant count filters
        if (filter.hasVariants === true) {
          where.variants = { some: {} }
        }
        if (filter.hasVariants === false) {
          where.variants = { none: {} }
        }
        
        // Image filters
        if (filter.hasImages === true) {
          where.images = { some: {} }
        }
        if (filter.hasImages === false) {
          where.images = { none: {} }
        }
        if (filter.hasPrimaryImage === true) {
          where.images = { some: { isPrimary: true } }
        }
        if (filter.hasPrimaryImage === false) {
          where.images = { none: { isPrimary: true } }
        }
      }
      
      // Build order by clause
      let orderBy: any = { createdAt: 'desc' }
      if (args.sort) {
        if (args.sort.field === 'price') {
          // For price sorting, we need to order by minimum variant price
          orderBy = {
            variants: {
              _min: {
                price: args.sort.direction.toLowerCase()
              }
            }
          }
        } else {
          orderBy = { [args.sort.field]: args.sort.direction.toLowerCase() }
        }
      }
      
      // Execute queries
      const [products, totalCount] = await Promise.all([
        prisma.product.findMany({
          where,
          orderBy,
          take,
          skip,
          include: {
            category: true,
            variants: {
              include: {
                size: true,
                color: true,
              },
            },
            images: true,
          },
        }),
        prisma.product.count({ where }),
      ])
      
      return {
        products,
        pageInfo: createPageInfo(totalCount, page, pageSize),
      }
    },
  })
)

// Product Mutations
builder.mutationField('createProduct', (t) =>
  t.prismaField({
    type: 'Product',
    args: {
      input: t.arg({ type: CreateProductInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      // Validate category exists
      const category = await prisma.category.findUnique({
        where: { id: args.input.categoryId },
      })
      
      if (!category) {
        throw new Error('Category not found')
      }

      return prisma.product.create({
        ...query,
        data: {
          name: args.input.name,
          description: args.input.description,
          categoryId: args.input.categoryId,
        },
      })
    },
  })
)

builder.mutationField('updateProduct', (t) =>
  t.prismaField({
    type: 'Product',
    args: {
      input: t.arg({ type: UpdateProductInput, required: true }),
    },
    resolve: async (query, _parent, args) => {
      const { id, ...data } = args.input
      
      // Filter out undefined fields
      const updateData = Object.fromEntries(
        Object.entries(data).filter(([_, value]) => value !== undefined)
      )

      // Validate category if provided
      if (updateData.categoryId) {
        const category = await prisma.category.findUnique({
          where: { id: updateData.categoryId },
        })
        
        if (!category) {
          throw new Error('Category not found')
        }
      }

      return prisma.product.update({
        where: { id },
        data: updateData,
      })
    },
  })
)

builder.mutationField('deleteProduct', (t) =>
  t.prismaField({
    type: 'Product',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, _parent, args) => {
      return prisma.product.delete({
        where: { id: args.id },
      })
    },
  })
)
