/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { builder } from '../../lib/builder';
import { prisma } from '../../lib/prisma';
import { z } from 'zod';
import { Size, Color } from './common';
import {
  PaginationInput,
  Pagination,
  SortInput,
  DateRangeInput,
  StringFilterInput,
  NumberRangeInput,
  calculateTraditionalPagination,
  calculateCursorPagination,
  createPageInfo,
  PaginationInputSchema,
} from './pagination';

// Product object type
export const Product = builder.prismaObject('Product', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    description: t.exposeString('description'),
    categoryId: t.exposeString('categoryId'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    // Relations
    category: t.relation('category'),
    variants: t.relation('variants'),
    images: t.relation('images'),
    tags: t.relation('tags'),
    reviews: t.relation('reviews'),
    // Computed fields
    averageRating: t.float({
      nullable: true,
      resolve: async (product, args, ctx) => {
        const reviews = await ctx.prisma.review.findMany({
          where: { productId: product.id },
          select: { rating: true },
        });
        if (reviews.length === 0) return null;
        return reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;
      },
    }),
    reviewCount: t.int({
      resolve: async (product, args, ctx) => {
        return ctx.prisma.review.count({
          where: { productId: product.id },
        });
      },
    }),
    minPrice: t.float({
      nullable: true,
      resolve: async (product, args, ctx) => {
        const variant = await ctx.prisma.productVariant.findFirst({
          where: { productId: product.id },
          orderBy: { price: 'asc' },
          select: { price: true },
        });
        return variant?.price || null;
      },
    }),
    maxPrice: t.float({
      nullable: true,
      resolve: async (product, args, ctx) => {
        const variant = await ctx.prisma.productVariant.findFirst({
          where: { productId: product.id },
          orderBy: { price: 'desc' },
          select: { price: true },
        });
        return variant?.price || null;
      },
    }),
    primaryImage: t.field({
      type: 'ProductImage',
      nullable: true,
      resolve: async (product, args, ctx) => {
        return ctx.prisma.productImage.findFirst({
          where: {
            productId: product.id,
            isPrimary: true,
          },
        });
      },
    }),
  }),
});

// ProductImage object type
export const ProductImage = builder.prismaObject('ProductImage', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productId: t.exposeString('productId'),
    imageUrl: t.exposeString('imageUrl'),
    isPrimary: t.exposeBoolean('isPrimary'),
    product: t.relation('product'),
  }),
});

// ProductVariant object type
export const ProductVariant = builder.prismaObject('ProductVariant', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productId: t.exposeString('productId'),
    sizeId: t.exposeString('sizeId'),
    colorId: t.exposeString('colorId'),
    sku: t.exposeString('sku'),
    price: t.exposeFloat('price'),
    quantity: t.exposeInt('quantity'),
    product: t.relation('product'),
    size: t.relation('size'),
    color: t.relation('color'),
    discounts: t.relation('discounts'),
    wishlistItems: t.relation('wishlistItems'),
    orderItems: t.relation('orderItems'),
    // Computed fields
    isInStock: t.boolean({
      resolve: (variant) => variant.quantity > 0,
    }),
    discountedPrice: t.float({
      nullable: true,
      resolve: async (variant, args, ctx) => {
        const activeDiscounts = await ctx.prisma.variantDiscount.findMany({
          where: {
            productVariantId: variant.id,
            discount: {
              startDate: { lte: new Date() },
              endDate: { gte: new Date() },
            },
          },
          include: { discount: true },
        });

        if (activeDiscounts.length === 0) return null;

        let finalPrice = variant.price;
        for (const variantDiscount of activeDiscounts) {
          const discount = variantDiscount.discount;
          if (discount.discountPercent) {
            finalPrice = finalPrice * (1 - discount.discountPercent / 100);
          } else if (discount.discountAmount) {
            finalPrice = Math.max(0, finalPrice - discount.discountAmount);
          }
        }

        return finalPrice;
      },
    }),
  }),
});

// Size and Color types are now defined in common.ts

// Category object type
export const Category = builder.prismaObject('Category', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    products: t.relation('products'),
    productCount: t.int({
      resolve: async (category, args, ctx) => {
        return ctx.prisma.product.count({
          where: { categoryId: category.id },
        });
      },
    }),
  }),
});

// Tag type is now defined in common.ts

// Product filter input
const ProductFilterSchema = z.object({
  id: z.string().optional(),
  name: z.object({
    equals: z.string().optional(),
    contains: z.string().optional(),
    startsWith: z.string().optional(),
    endsWith: z.string().optional(),
    in: z.array(z.string()).optional(),
    notIn: z.array(z.string()).optional(),
  }).optional(),
  description: z.object({
    equals: z.string().optional(),
    contains: z.string().optional(),
    startsWith: z.string().optional(),
    endsWith: z.string().optional(),
  }).optional(),
  categoryId: z.string().optional(),
  categoryIds: z.array(z.string()).optional(),
  priceRange: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
  }).optional(),
  tags: z.array(z.string()).optional(),
  inStock: z.boolean().optional(),
  hasDiscount: z.boolean().optional(),
  ratingMin: z.number().min(1).max(5).optional(),
  createdAt: z.object({
    from: z.date().optional(),
    to: z.date().optional(),
  }).optional(),
  search: z.string().optional(),
  sizeIds: z.array(z.string()).optional(),
  colorIds: z.array(z.string()).optional(),
});

export const ProductFilterInput = builder.inputType('ProductFilterInput', {
  fields: (t) => ({
    id: t.string({ description: 'Filter by product ID' }),
    name: t.field({ type: StringFilterInput, description: 'Filter by product name' }),
    description: t.field({ type: StringFilterInput, description: 'Filter by product description' }),
    categoryId: t.string({ description: 'Filter by category ID' }),
    categoryIds: t.stringList({ description: 'Filter by multiple category IDs' }),
    priceRange: t.field({ type: NumberRangeInput, description: 'Filter by price range' }),
    tags: t.stringList({ description: 'Filter by tag names' }),
    inStock: t.boolean({ description: 'Filter by stock availability' }),
    hasDiscount: t.boolean({ description: 'Filter products with active discounts' }),
    ratingMin: t.float({ description: 'Minimum average rating' }),
    createdAt: t.field({ type: DateRangeInput, description: 'Filter by creation date range' }),
    search: t.string({ description: 'Global search across name and description' }),
    sizeIds: t.stringList({ description: 'Filter by available size IDs' }),
    colorIds: t.stringList({ description: 'Filter by available color IDs' }),
  }),
});

// Product sort fields enum
export const ProductSortField = builder.enumType('ProductSortField', {
  values: {
    ID: { value: 'id' },
    NAME: { value: 'name' },
    CREATED_AT: { value: 'createdAt' },
    UPDATED_AT: { value: 'updatedAt' },
    PRICE: { value: 'price' },
    RATING: { value: 'rating' },
  },
});

// Product connection type for cursor-based pagination
export const ProductConnection = builder.connectionObject({
  type: Product,
  name: 'ProductConnection',
});

// Product list with traditional pagination
export const ProductList = builder.objectType('ProductList', {
  fields: (t) => ({
    items: t.field({ type: [Product], description: 'List of products' }),
    pagination: t.field({ type: Pagination, description: 'Pagination information' }),
  }),
});

// Helper function to build Prisma where clause for products
function buildProductWhereClause(filter: any) {
  const where: any = {};

  if (filter?.id) {
    where.id = filter.id;
  }

  if (filter?.name) {
    where.name = buildStringFilter(filter.name);
  }

  if (filter?.description) {
    where.description = buildStringFilter(filter.description);
  }

  if (filter?.categoryId) {
    where.categoryId = filter.categoryId;
  }

  if (filter?.categoryIds?.length) {
    where.categoryId = { in: filter.categoryIds };
  }

  if (filter?.tags?.length) {
    where.tags = {
      some: {
        tag: {
          name: { in: filter.tags },
        },
      },
    };
  }

  if (filter?.priceRange) {
    where.variants = {
      some: {
        price: {
          ...(filter.priceRange.min && { gte: filter.priceRange.min }),
          ...(filter.priceRange.max && { lte: filter.priceRange.max }),
        },
      },
    };
  }

  if (filter?.inStock !== undefined) {
    if (filter.inStock) {
      where.variants = {
        some: { quantity: { gt: 0 } },
      };
    } else {
      where.variants = {
        every: { quantity: { lte: 0 } },
      };
    }
  }

  if (filter?.hasDiscount) {
    where.variants = {
      some: {
        discounts: {
          some: {
            discount: {
              startDate: { lte: new Date() },
              endDate: { gte: new Date() },
            },
          },
        },
      },
    };
  }

  if (filter?.sizeIds?.length) {
    where.variants = {
      some: {
        sizeId: { in: filter.sizeIds },
      },
    };
  }

  if (filter?.colorIds?.length) {
    where.variants = {
      some: {
        colorId: { in: filter.colorIds },
      },
    };
  }

  if (filter?.createdAt) {
    where.createdAt = {};
    if (filter.createdAt.from) {
      where.createdAt.gte = filter.createdAt.from;
    }
    if (filter.createdAt.to) {
      where.createdAt.lte = filter.createdAt.to;
    }
  }

  if (filter?.search) {
    where.OR = [
      { name: { contains: filter.search, mode: 'insensitive' } },
      { description: { contains: filter.search, mode: 'insensitive' } },
    ];
  }

  return where;
}

function buildStringFilter(filter: any) {
  const result: any = {};

  if (filter.equals) result.equals = filter.equals;
  if (filter.contains) result.contains = filter.contains;
  if (filter.startsWith) result.startsWith = filter.startsWith;
  if (filter.endsWith) result.endsWith = filter.endsWith;
  if (filter.in) result.in = filter.in;
  if (filter.notIn) result.notIn = filter.notIn;

  if (filter.contains || filter.startsWith || filter.endsWith) {
    result.mode = 'insensitive';
  }

  return result;
}

function buildProductOrderByClause(sort?: { field: string; direction: 'asc' | 'desc' }[]) {
  if (!sort?.length) {
    return [{ createdAt: 'desc' as const }];
  }

  return sort.map(s => {
    if (s.field === 'price') {
      return {
        variants: {
          _min: { price: s.direction },
        },
      };
    }
    return { [s.field]: s.direction };
  });
}

// Query fields for products
builder.queryFields((t) => ({
  // Single product query
  product: t.prismaField({
    type: 'Product',
    nullable: true,
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (query, root, args, ctx) => {
      return ctx.prisma.product.findUnique({
        ...query,
        where: { id: args.id },
      });
    },
  }),

  // Products with cursor-based pagination
  products: t.connection({
    type: Product,
    args: {
      filter: t.arg({ type: ProductFilterInput }),
      sort: t.arg({ type: [SortInput] }),
    },
    resolve: async (root, args, ctx) => {
      const filter = args.filter;
      const sort = args.sort;
      
      const where = buildProductWhereClause(filter);
      const orderBy = buildProductOrderByClause(sort);

      // Get total count
      const totalCount = await ctx.prisma.product.count({ where });

      // Calculate pagination
      const { skip, take } = calculateCursorPagination(args, totalCount);

      // Get products
      const products = await ctx.prisma.product.findMany({
        where,
        orderBy,
        skip,
        take,
      });

      // Create page info
      const pageInfo = createPageInfo(products, totalCount, skip, take);

      return {
        edges: products.map((product, index) => ({
          node: product,
          cursor: pageInfo.startCursor || '',
        })),
        pageInfo,
      };
    },
  }),

  // Products with traditional pagination
  productsList: t.field({
    type: ProductList,
    args: {
      pagination: t.arg({ type: PaginationInput }),
      filter: t.arg({ type: ProductFilterInput }),
      sort: t.arg({ type: [SortInput] }),
    },
    validate: {
      schema: z.object({
        pagination: PaginationInputSchema.optional(),
        filter: ProductFilterSchema.optional(),
        sort: z.array(z.object({
          field: z.string(),
          direction: z.enum(['asc', 'desc']),
        })).optional(),
      }),
    },
    resolve: async (root, args, ctx) => {
      const { pagination = {}, filter, sort } = args;
      
      const where = buildProductWhereClause(filter);
      const orderBy = buildProductOrderByClause(sort);

      // Get total count
      const totalCount = await ctx.prisma.product.count({ where });

      // Calculate pagination
      const { skip, take, pagination: paginationInfo } = calculateTraditionalPagination(
        pagination.page || 1,
        pagination.pageSize || 10,
        totalCount,
        pagination.offset,
        pagination.skip
      );

      // Get products
      const products = await ctx.prisma.product.findMany({
        where,
        orderBy,
        skip,
        take,
      });

      return {
        items: products,
        pagination: paginationInfo,
      };
    },
  }),

  // Categories query
  categories: t.prismaField({
    type: ['Category'],
    resolve: async (query, root, args, ctx) => {
      return ctx.prisma.category.findMany({
        ...query,
        orderBy: { name: 'asc' },
      });
    },
  }),

  // Sizes query
  sizes: t.prismaField({
    type: ['Size'],
    resolve: async (query, root, args, ctx) => {
      return ctx.prisma.size.findMany({
        ...query,
        orderBy: { sizeLabel: 'asc' },
      });
    },
  }),

  // Colors query
  colors: t.prismaField({
    type: ['Color'],
    resolve: async (query, root, args, ctx) => {
      return ctx.prisma.color.findMany({
        ...query,
        orderBy: { colorName: 'asc' },
      });
    },
  }),

  // Tags query
  tags: t.prismaField({
    type: ['Tag'],
    resolve: async (query, root, args, ctx) => {
      return ctx.prisma.tag.findMany({
        ...query,
        orderBy: { name: 'asc' },
      });
    },
  }),
}));
