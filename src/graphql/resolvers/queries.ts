import { builder } from '../../lib/builder'
import {
  calculateTraditionalPagination,
  calculateCursorPagination,
  createCursorPageInfo
} from '../../lib/pagination'
import { createSearchFilter } from '../../lib/filters'
import { Category, CategoryConnection } from '../types/category'
import { Product, ProductConnection } from '../types/product'
import { ProductVariant } from '../types/productVariant'
import { Customer } from '../types/customer'
import { Size, Color } from '../types/common'

// Category Queries
builder.queryField('categories', (t) =>
  t.field({
    type: [Category],
    args: {
      where: t.arg({ type: 'CategoryWhereInput', required: false }),
      orderBy: t.arg({ type: ['CategoryOrderByInput'], required: false }),
      pagination: t.arg({ type: 'PaginationInput', required: false }),
      search: t.string({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      const { take, skip } = calculateTraditionalPagination(args.pagination || {})
      
      let where = args.where || {}
      
      // Add search functionality
      if (args.search) {
        const searchFilter = createSearchFilter(args.search, ['name', 'description'])
        where = { ...where, ...searchFilter }
      }
      
      return prisma.category.findMany({
        where,
        orderBy: args.orderBy || [{ sortOrder: 'asc' }, { name: 'asc' }],
        take,
        skip,
      })
    },
  })
)

builder.queryField('category', (t) =>
  t.field({
    type: Category,
    nullable: true,
    args: {
      id: t.arg.string({ required: false }),
      slug: t.arg.string({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      if (!args.id && !args.slug) {
        throw new Error('Either id or slug must be provided')
      }
      
      return prisma.category.findFirst({
        where: args.id ? { id: args.id } : { slug: args.slug },
      })
    },
  })
)

builder.queryField('categoriesConnection', (t) =>
  t.field({
    type: CategoryConnection,
    args: {
      where: t.arg({ type: 'CategoryWhereInput', required: false }),
      orderBy: t.arg({ type: ['CategoryOrderByInput'], required: false }),
      pagination: t.arg({ type: 'CursorPaginationInput', required: false }),
      search: t.string({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      const { take, cursor, skip } = calculateCursorPagination(args.pagination || {})
      
      let where = args.where || {}
      
      if (args.search) {
        const searchFilter = createSearchFilter(args.search, ['name', 'description'])
        where = { ...where, ...searchFilter }
      }
      
      const categories = await prisma.category.findMany({
        where,
        orderBy: args.orderBy || [{ sortOrder: 'asc' }, { name: 'asc' }],
        take: take + 1, // Take one extra to check if there are more
        cursor,
        skip,
      })
      
      const hasMore = categories.length > take
      const items = hasMore ? categories.slice(0, -1) : categories
      
      const edges = items.map((category) => ({
        node: category,
        cursor: category.id,
      }))
      
      const pageInfo = createCursorPageInfo(items, hasMore, skip > 0)
      
      const totalCount = await prisma.category.count({ where })
      
      return {
        edges,
        pageInfo,
        totalCount,
      }
    },
  })
)

// Product Queries
builder.queryField('products', (t) =>
  t.field({
    type: [Product],
    args: {
      where: t.arg({ type: 'ProductWhereInput', required: false }),
      orderBy: t.arg({ type: ['ProductOrderByInput'], required: false }),
      pagination: t.arg({ type: 'PaginationInput', required: false }),
      search: t.string({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      const { take, skip } = calculateTraditionalPagination(args.pagination || {})
      
      let where = args.where || {}
      
      // Handle search
      if (args.search) {
        const searchFilter = createSearchFilter(args.search, ['name', 'description', 'brand', 'tags'])
        where = { ...where, ...searchFilter }
      }
      
      // Handle price range filters
      if (args.where?.minPrice || args.where?.maxPrice) {
        where.variants = {
          some: {
            price: {
              ...(args.where.minPrice && { gte: args.where.minPrice }),
              ...(args.where.maxPrice && { lte: args.where.maxPrice }),
            }
          }
        }
      }
      
      return prisma.product.findMany({
        where,
        orderBy: args.orderBy || [{ isFeatured: 'desc' }, { createdAt: 'desc' }],
        take,
        skip,
        include: {
          category: true,
          variants: {
            where: { isActive: true },
            include: { size: true, color: true }
          },
          images: {
            orderBy: [{ isPrimary: 'desc' }, { sortOrder: 'asc' }]
          }
        }
      })
    },
  })
)

builder.queryField('product', (t) =>
  t.field({
    type: Product,
    nullable: true,
    args: {
      id: t.arg.string({ required: false }),
      slug: t.arg.string({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      if (!args.id && !args.slug) {
        throw new Error('Either id or slug must be provided')
      }
      
      return prisma.product.findFirst({
        where: args.id ? { id: args.id } : { slug: args.slug },
        include: {
          category: true,
          variants: {
            where: { isActive: true },
            include: { size: true, color: true }
          },
          images: {
            orderBy: [{ isPrimary: 'desc' }, { sortOrder: 'asc' }]
          },
          reviews: {
            where: { isApproved: true },
            include: { customer: true },
            orderBy: { createdAt: 'desc' }
          }
        }
      })
    },
  })
)

builder.queryField('productsConnection', (t) =>
  t.field({
    type: ProductConnection,
    args: {
      where: t.arg({ type: 'ProductWhereInput', required: false }),
      orderBy: t.arg({ type: ['ProductOrderByInput'], required: false }),
      pagination: t.arg({ type: 'CursorPaginationInput', required: false }),
      search: t.string({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      const { take, cursor, skip } = calculateCursorPagination(args.pagination || {})
      
      let where = args.where || {}
      
      if (args.search) {
        const searchFilter = createSearchFilter(args.search, ['name', 'description', 'brand', 'tags'])
        where = { ...where, ...searchFilter }
      }
      
      if (args.where?.minPrice || args.where?.maxPrice) {
        where.variants = {
          some: {
            price: {
              ...(args.where.minPrice && { gte: args.where.minPrice }),
              ...(args.where.maxPrice && { lte: args.where.maxPrice }),
            }
          }
        }
      }
      
      const products = await prisma.product.findMany({
        where,
        orderBy: args.orderBy || [{ isFeatured: 'desc' }, { createdAt: 'desc' }],
        take: take + 1,
        cursor,
        skip,
        include: {
          category: true,
          variants: {
            where: { isActive: true },
            include: { size: true, color: true }
          },
          images: {
            orderBy: [{ isPrimary: 'desc' }, { sortOrder: 'asc' }]
          }
        }
      })
      
      const hasMore = products.length > take
      const items = hasMore ? products.slice(0, -1) : products
      
      const edges = items.map((product) => ({
        node: product,
        cursor: product.id,
      }))
      
      const pageInfo = createCursorPageInfo(items, hasMore, skip > 0)
      const totalCount = await prisma.product.count({ where })
      
      return {
        edges,
        pageInfo,
        totalCount,
      }
    },
  })
)

// ProductVariant Queries
builder.queryField('productVariants', (t) =>
  t.field({
    type: [ProductVariant],
    args: {
      productId: t.arg.string({ required: false }),
      pagination: t.arg({ type: 'PaginationInput', required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      const { take, skip } = calculateTraditionalPagination(args.pagination || {})

      return prisma.productVariant.findMany({
        where: args.productId ? { productId: args.productId } : {},
        orderBy: [{ createdAt: 'desc' }],
        take,
        skip,
        include: {
          product: true,
          size: true,
          color: true,
        }
      })
    },
  })
)

// Size and Color Queries
builder.queryField('sizes', (t) =>
  t.field({
    type: [Size],
    args: {
      isActive: t.arg.boolean({ required: false }),
      category: t.arg.string({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      return prisma.size.findMany({
        where: {
          isActive: args.isActive ?? true,
          ...(args.category && { category: args.category })
        },
        orderBy: [{ sortOrder: 'asc' }, { sizeLabel: 'asc' }],
      })
    },
  })
)

builder.queryField('colors', (t) =>
  t.field({
    type: [Color],
    args: {
      isActive: t.arg.boolean({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      return prisma.color.findMany({
        where: {
          isActive: args.isActive ?? true,
        },
        orderBy: [{ sortOrder: 'asc' }, { colorName: 'asc' }],
      })
    },
  })
)
