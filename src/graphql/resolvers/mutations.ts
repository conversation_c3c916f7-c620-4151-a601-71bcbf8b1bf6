import { builder } from '../../lib/builder'
import { Category, CategoryCreateInput, CategoryUpdateInput } from '../types/category'
import { Product, ProductCreateInput, ProductUpdateInput } from '../types/product'
import { ProductVariant } from '../types/productVariant'
import { Customer } from '../types/customer'
import { Size, Color } from '../types/common'

// Category Mutations
builder.mutationField('createCategory', (t) =>
  t.field({
    type: Category,
    args: {
      input: t.arg({ type: CategoryCreateInput, required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if slug already exists
        const existingCategory = await prisma.category.findUnique({
          where: { slug: args.input.slug }
        })
        
        if (existingCategory) {
          throw new Error('Category with this slug already exists')
        }
        
        return await prisma.category.create({
          data: {
            name: args.input.name,
            description: args.input.description,
            slug: args.input.slug,
            isActive: args.input.isActive ?? true,
            sortOrder: args.input.sortOrder ?? 0,
            parentId: args.input.parentId,
          },
        })
      } catch (error) {
        throw new Error(`Failed to create category: ${(error as Error).message}`)
      }
    },
  })
)

builder.mutationField('updateCategory', (t) =>
  t.field({
    type: Category,
    args: {
      id: t.arg.string({ required: true }),
      input: t.arg({ type: CategoryUpdateInput, required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if category exists
        const existingCategory = await prisma.category.findUnique({
          where: { id: args.id }
        })
        
        if (!existingCategory) {
          throw new Error('Category not found')
        }
        
        // Check if slug is being updated and if it conflicts
        if (args.input.slug && args.input.slug !== existingCategory.slug) {
          const slugConflict = await prisma.category.findUnique({
            where: { slug: args.input.slug }
          })
          
          if (slugConflict) {
            throw new Error('Category with this slug already exists')
          }
        }
        
        return await prisma.category.update({
          where: { id: args.id },
          data: {
            ...(args.input.name && { name: args.input.name }),
            ...(args.input.description !== undefined && { description: args.input.description }),
            ...(args.input.slug && { slug: args.input.slug }),
            ...(args.input.isActive !== undefined && { isActive: args.input.isActive }),
            ...(args.input.sortOrder !== undefined && { sortOrder: args.input.sortOrder }),
            ...(args.input.parentId !== undefined && { parentId: args.input.parentId || null }),
          },
        })
      } catch (error) {
        throw new Error(`Failed to update category: ${(error as Error).message}`)
      }
    },
  })
)

builder.mutationField('deleteCategory', (t) =>
  t.field({
    type: 'Boolean',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if category has products
        const productCount = await prisma.product.count({
          where: { categoryId: args.id }
        })
        
        if (productCount > 0) {
          throw new Error('Cannot delete category that has products')
        }
        
        // Check if category has children
        const childrenCount = await prisma.category.count({
          where: { parentId: args.id }
        })
        
        if (childrenCount > 0) {
          throw new Error('Cannot delete category that has subcategories')
        }
        
        await prisma.category.delete({
          where: { id: args.id }
        })
        
        return true
      } catch (error) {
        throw new Error(`Failed to delete category: ${(error as Error).message}`)
      }
    },
  })
)

// Product Mutations
builder.mutationField('createProduct', (t) =>
  t.field({
    type: Product,
    args: {
      input: t.arg({ type: ProductCreateInput, required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if slug already exists
        const existingProduct = await prisma.product.findUnique({
          where: { slug: args.input.slug }
        })
        
        if (existingProduct) {
          throw new Error('Product with this slug already exists')
        }
        
        // Check if SKU already exists (if provided)
        if (args.input.sku) {
          const existingSku = await prisma.product.findUnique({
            where: { sku: args.input.sku }
          })
          
          if (existingSku) {
            throw new Error('Product with this SKU already exists')
          }
        }
        
        // Verify category exists
        const category = await prisma.category.findUnique({
          where: { id: args.input.categoryId }
        })
        
        if (!category) {
          throw new Error('Category not found')
        }
        
        return await prisma.product.create({
          data: {
            name: args.input.name,
            description: args.input.description,
            slug: args.input.slug,
            sku: args.input.sku,
            brand: args.input.brand,
            tags: args.input.tags || [],
            isActive: args.input.isActive ?? true,
            isFeatured: args.input.isFeatured ?? false,
            weight: args.input.weight,
            dimensions: args.input.dimensions as any,
            material: args.input.material,
            careInstructions: args.input.careInstructions,
            categoryId: args.input.categoryId,
          },
          include: {
            category: true,
            variants: true,
            images: true,
          }
        })
      } catch (error) {
        throw new Error(`Failed to create product: ${(error as Error).message}`)
      }
    },
  })
)

builder.mutationField('updateProduct', (t) =>
  t.field({
    type: Product,
    args: {
      id: t.arg.string({ required: true }),
      input: t.arg({ type: ProductUpdateInput, required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if product exists
        const existingProduct = await prisma.product.findUnique({
          where: { id: args.id }
        })
        
        if (!existingProduct) {
          throw new Error('Product not found')
        }
        
        // Check slug conflicts
        if (args.input.slug && args.input.slug !== existingProduct.slug) {
          const slugConflict = await prisma.product.findUnique({
            where: { slug: args.input.slug }
          })
          
          if (slugConflict) {
            throw new Error('Product with this slug already exists')
          }
        }
        
        // Check SKU conflicts
        if (args.input.sku && args.input.sku !== existingProduct.sku) {
          const skuConflict = await prisma.product.findUnique({
            where: { sku: args.input.sku }
          })
          
          if (skuConflict) {
            throw new Error('Product with this SKU already exists')
          }
        }
        
        // Verify category exists if being updated
        if (args.input.categoryId) {
          const category = await prisma.category.findUnique({
            where: { id: args.input.categoryId }
          })
          
          if (!category) {
            throw new Error('Category not found')
          }
        }
        
        const updateData: any = {}
        if (args.input.name) updateData.name = args.input.name
        if (args.input.description) updateData.description = args.input.description
        if (args.input.slug) updateData.slug = args.input.slug
        if (args.input.sku !== undefined) updateData.sku = args.input.sku
        if (args.input.brand !== undefined) updateData.brand = args.input.brand
        if (args.input.tags !== undefined) updateData.tags = args.input.tags
        if (args.input.isActive !== undefined) updateData.isActive = args.input.isActive
        if (args.input.isFeatured !== undefined) updateData.isFeatured = args.input.isFeatured
        if (args.input.weight !== undefined) updateData.weight = args.input.weight
        if (args.input.dimensions !== undefined) updateData.dimensions = args.input.dimensions
        if (args.input.material !== undefined) updateData.material = args.input.material
        if (args.input.careInstructions !== undefined) updateData.careInstructions = args.input.careInstructions
        if (args.input.categoryId) updateData.categoryId = args.input.categoryId

        return await prisma.product.update({
          where: { id: args.id },
          data: updateData,
          include: {
            category: true,
            variants: true,
            images: true,
          }
        })
      } catch (error) {
        throw new Error(`Failed to update product: ${(error as Error).message}`)
      }
    },
  })
)

builder.mutationField('deleteProduct', (t) =>
  t.field({
    type: 'Boolean',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if product has orders
        const orderItemCount = await prisma.orderItem.count({
          where: {
            productVariant: {
              productId: args.id
            }
          }
        })
        
        if (orderItemCount > 0) {
          throw new Error('Cannot delete product that has been ordered')
        }
        
        // Delete related data in transaction
        await prisma.$transaction(async (tx) => {
          // Delete product variants
          await tx.productVariant.deleteMany({
            where: { productId: args.id }
          })
          
          // Delete product images
          await tx.productImage.deleteMany({
            where: { productId: args.id }
          })
          
          // Delete product reviews
          await tx.productReview.deleteMany({
            where: { productId: args.id }
          })
          
          // Delete wishlist items
          await tx.wishlistItem.deleteMany({
            where: { productId: args.id }
          })
          
          // Finally delete the product
          await tx.product.delete({
            where: { id: args.id }
          })
        })
        
        return true
      } catch (error) {
        throw new Error(`Failed to delete product: ${(error as Error).message}`)
      }
    },
  })
)

// Customer Mutations
builder.mutationField('createCustomer', (t) =>
  t.field({
    type: Customer,
    args: {
      email: t.arg.string({ required: true }),
      firstName: t.arg.string({ required: true }),
      lastName: t.arg.string({ required: true }),
      phone: t.arg.string({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if email already exists
        const existingCustomer = await prisma.customer.findUnique({
          where: { email: args.email }
        })

        if (existingCustomer) {
          throw new Error('Customer with this email already exists')
        }

        return await prisma.customer.create({
          data: {
            email: args.email,
            firstName: args.firstName,
            lastName: args.lastName,
            phone: args.phone,
          },
        })
      } catch (error) {
        throw new Error(`Failed to create customer: ${(error as Error).message}`)
      }
    },
  })
)

// Size and Color Mutations
builder.mutationField('createSize', (t) =>
  t.field({
    type: Size,
    args: {
      sizeLabel: t.arg.string({ required: true }),
      category: t.arg.string({ required: false }),
      sortOrder: t.arg.int({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        const existingSize = await prisma.size.findUnique({
          where: { sizeLabel: args.sizeLabel }
        })

        if (existingSize) {
          throw new Error('Size with this label already exists')
        }

        return await prisma.size.create({
          data: {
            sizeLabel: args.sizeLabel,
            category: args.category,
            sortOrder: args.sortOrder ?? 0,
          },
        })
      } catch (error) {
        throw new Error(`Failed to create size: ${(error as Error).message}`)
      }
    },
  })
)

builder.mutationField('createColor', (t) =>
  t.field({
    type: Color,
    args: {
      colorName: t.arg.string({ required: true }),
      hexCode: t.arg.string({ required: false }),
      sortOrder: t.arg.int({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        const existingColor = await prisma.color.findUnique({
          where: { colorName: args.colorName }
        })

        if (existingColor) {
          throw new Error('Color with this name already exists')
        }

        return await prisma.color.create({
          data: {
            colorName: args.colorName,
            hexCode: args.hexCode,
            sortOrder: args.sortOrder ?? 0,
          },
        })
      } catch (error) {
        throw new Error(`Failed to create color: ${(error as Error).message}`)
      }
    },
  })
)

// ProductVariant Mutations
builder.mutationField('createProductVariant', (t) =>
  t.field({
    type: ProductVariant,
    args: {
      productId: t.arg.string({ required: true }),
      sizeId: t.arg.string({ required: true }),
      colorId: t.arg.string({ required: true }),
      sku: t.arg.string({ required: true }),
      price: t.arg.float({ required: true }),
      compareAtPrice: t.arg.float({ required: false }),
      costPrice: t.arg.float({ required: false }),
      quantity: t.arg.int({ required: true }),
      lowStockThreshold: t.arg.int({ required: false }),
      isActive: t.arg.boolean({ required: false }),
      weight: t.arg.float({ required: false }),
      barcode: t.arg.string({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if SKU already exists
        const existingSku = await prisma.productVariant.findUnique({
          where: { sku: args.sku }
        })

        if (existingSku) {
          throw new Error('Product variant with this SKU already exists')
        }

        // Check if combination already exists
        const existingVariant = await prisma.productVariant.findFirst({
          where: {
            productId: args.productId,
            sizeId: args.sizeId,
            colorId: args.colorId,
          }
        })

        if (existingVariant) {
          throw new Error('Product variant with this size and color combination already exists')
        }

        // Verify related entities exist
        const [product, size, color] = await Promise.all([
          prisma.product.findUnique({ where: { id: args.productId } }),
          prisma.size.findUnique({ where: { id: args.sizeId } }),
          prisma.color.findUnique({ where: { id: args.colorId } }),
        ])

        if (!product) throw new Error('Product not found')
        if (!size) throw new Error('Size not found')
        if (!color) throw new Error('Color not found')

        return await prisma.productVariant.create({
          data: {
            productId: args.productId,
            sizeId: args.sizeId,
            colorId: args.colorId,
            sku: args.sku,
            price: args.price,
            compareAtPrice: args.compareAtPrice,
            costPrice: args.costPrice,
            quantity: args.quantity,
            lowStockThreshold: args.lowStockThreshold ?? 5,
            isActive: args.isActive ?? true,
            weight: args.weight,
            barcode: args.barcode,
          },
          include: {
            product: true,
            size: true,
            color: true,
          }
        })
      } catch (error) {
        throw new Error(`Failed to create product variant: ${(error as Error).message}`)
      }
    },
  })
)

builder.mutationField('updateProductVariant', (t) =>
  t.field({
    type: ProductVariant,
    args: {
      id: t.arg.string({ required: true }),
      sku: t.arg.string({ required: false }),
      price: t.arg.float({ required: false }),
      compareAtPrice: t.arg.float({ required: false }),
      costPrice: t.arg.float({ required: false }),
      quantity: t.arg.int({ required: false }),
      lowStockThreshold: t.arg.int({ required: false }),
      isActive: t.arg.boolean({ required: false }),
      weight: t.arg.float({ required: false }),
      barcode: t.arg.string({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if variant exists
        const existingVariant = await prisma.productVariant.findUnique({
          where: { id: args.id }
        })

        if (!existingVariant) {
          throw new Error('Product variant not found')
        }

        // Check SKU conflicts
        if (args.sku && args.sku !== existingVariant.sku) {
          const skuConflict = await prisma.productVariant.findUnique({
            where: { sku: args.sku }
          })

          if (skuConflict) {
            throw new Error('Product variant with this SKU already exists')
          }
        }

        return await prisma.productVariant.update({
          where: { id: args.id },
          data: {
            ...(args.sku && { sku: args.sku }),
            ...(args.price !== undefined && { price: args.price }),
            ...(args.compareAtPrice !== undefined && { compareAtPrice: args.compareAtPrice }),
            ...(args.costPrice !== undefined && { costPrice: args.costPrice }),
            ...(args.quantity !== undefined && { quantity: args.quantity }),
            ...(args.lowStockThreshold !== undefined && { lowStockThreshold: args.lowStockThreshold }),
            ...(args.isActive !== undefined && { isActive: args.isActive }),
            ...(args.weight !== undefined && { weight: args.weight }),
            ...(args.barcode !== undefined && { barcode: args.barcode }),
          },
          include: {
            product: true,
            size: true,
            color: true,
          }
        })
      } catch (error) {
        throw new Error(`Failed to update product variant: ${(error as Error).message}`)
      }
    },
  })
)

builder.mutationField('deleteProductVariant', (t) =>
  t.field({
    type: 'Boolean',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if variant has orders
        const orderItemCount = await prisma.orderItem.count({
          where: { productVariantId: args.id }
        })

        if (orderItemCount > 0) {
          throw new Error('Cannot delete product variant that has been ordered')
        }

        await prisma.productVariant.delete({
          where: { id: args.id }
        })

        return true
      } catch (error) {
        throw new Error(`Failed to delete product variant: ${(error as Error).message}`)
      }
    },
  })
)
