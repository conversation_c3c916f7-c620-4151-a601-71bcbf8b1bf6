import { builder } from '../../lib/builder'
import { Category } from '../types/category'
import { Product } from '../types/product'
import { ProductVariant } from '../types/productVariant'
import { Customer } from '../types/customer'
import { Size, Color } from '../types/common'

// Category Mutations
builder.mutationField('createCategory', (t) =>
  t.field({
    type: Category,
    args: {
      input: t.arg({ type: 'CategoryCreateInput', required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if slug already exists
        const existingCategory = await prisma.category.findUnique({
          where: { slug: args.input.slug }
        })
        
        if (existingCategory) {
          throw new Error('Category with this slug already exists')
        }
        
        return await prisma.category.create({
          data: {
            name: args.input.name,
            description: args.input.description,
            slug: args.input.slug,
            isActive: args.input.isActive ?? true,
            sortOrder: args.input.sortOrder ?? 0,
            parentId: args.input.parentId,
          },
        })
      } catch (error) {
        throw new Error(`Failed to create category: ${error.message}`)
      }
    },
  })
)

builder.mutationField('updateCategory', (t) =>
  t.field({
    type: Category,
    args: {
      id: t.arg.string({ required: true }),
      input: t.arg({ type: 'CategoryUpdateInput', required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if category exists
        const existingCategory = await prisma.category.findUnique({
          where: { id: args.id }
        })
        
        if (!existingCategory) {
          throw new Error('Category not found')
        }
        
        // Check if slug is being updated and if it conflicts
        if (args.input.slug && args.input.slug !== existingCategory.slug) {
          const slugConflict = await prisma.category.findUnique({
            where: { slug: args.input.slug }
          })
          
          if (slugConflict) {
            throw new Error('Category with this slug already exists')
          }
        }
        
        return await prisma.category.update({
          where: { id: args.id },
          data: {
            ...(args.input.name && { name: args.input.name }),
            ...(args.input.description !== undefined && { description: args.input.description }),
            ...(args.input.slug && { slug: args.input.slug }),
            ...(args.input.isActive !== undefined && { isActive: args.input.isActive }),
            ...(args.input.sortOrder !== undefined && { sortOrder: args.input.sortOrder }),
            ...(args.input.parentId !== undefined && { parentId: args.input.parentId }),
          },
        })
      } catch (error) {
        throw new Error(`Failed to update category: ${error.message}`)
      }
    },
  })
)

builder.mutationField('deleteCategory', (t) =>
  t.field({
    type: 'Boolean',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if category has products
        const productCount = await prisma.product.count({
          where: { categoryId: args.id }
        })
        
        if (productCount > 0) {
          throw new Error('Cannot delete category that has products')
        }
        
        // Check if category has children
        const childrenCount = await prisma.category.count({
          where: { parentId: args.id }
        })
        
        if (childrenCount > 0) {
          throw new Error('Cannot delete category that has subcategories')
        }
        
        await prisma.category.delete({
          where: { id: args.id }
        })
        
        return true
      } catch (error) {
        throw new Error(`Failed to delete category: ${error.message}`)
      }
    },
  })
)

// Product Mutations
builder.mutationField('createProduct', (t) =>
  t.field({
    type: Product,
    args: {
      input: t.arg({ type: 'ProductCreateInput', required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if slug already exists
        const existingProduct = await prisma.product.findUnique({
          where: { slug: args.input.slug }
        })
        
        if (existingProduct) {
          throw new Error('Product with this slug already exists')
        }
        
        // Check if SKU already exists (if provided)
        if (args.input.sku) {
          const existingSku = await prisma.product.findUnique({
            where: { sku: args.input.sku }
          })
          
          if (existingSku) {
            throw new Error('Product with this SKU already exists')
          }
        }
        
        // Verify category exists
        const category = await prisma.category.findUnique({
          where: { id: args.input.categoryId }
        })
        
        if (!category) {
          throw new Error('Category not found')
        }
        
        return await prisma.product.create({
          data: {
            name: args.input.name,
            description: args.input.description,
            slug: args.input.slug,
            sku: args.input.sku,
            brand: args.input.brand,
            tags: args.input.tags || [],
            isActive: args.input.isActive ?? true,
            isFeatured: args.input.isFeatured ?? false,
            weight: args.input.weight,
            dimensions: args.input.dimensions,
            material: args.input.material,
            careInstructions: args.input.careInstructions,
            categoryId: args.input.categoryId,
          },
          include: {
            category: true,
            variants: true,
            images: true,
          }
        })
      } catch (error) {
        throw new Error(`Failed to create product: ${error.message}`)
      }
    },
  })
)

builder.mutationField('updateProduct', (t) =>
  t.field({
    type: Product,
    args: {
      id: t.arg.string({ required: true }),
      input: t.arg({ type: 'ProductUpdateInput', required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if product exists
        const existingProduct = await prisma.product.findUnique({
          where: { id: args.id }
        })
        
        if (!existingProduct) {
          throw new Error('Product not found')
        }
        
        // Check slug conflicts
        if (args.input.slug && args.input.slug !== existingProduct.slug) {
          const slugConflict = await prisma.product.findUnique({
            where: { slug: args.input.slug }
          })
          
          if (slugConflict) {
            throw new Error('Product with this slug already exists')
          }
        }
        
        // Check SKU conflicts
        if (args.input.sku && args.input.sku !== existingProduct.sku) {
          const skuConflict = await prisma.product.findUnique({
            where: { sku: args.input.sku }
          })
          
          if (skuConflict) {
            throw new Error('Product with this SKU already exists')
          }
        }
        
        // Verify category exists if being updated
        if (args.input.categoryId) {
          const category = await prisma.category.findUnique({
            where: { id: args.input.categoryId }
          })
          
          if (!category) {
            throw new Error('Category not found')
          }
        }
        
        return await prisma.product.update({
          where: { id: args.id },
          data: {
            ...(args.input.name && { name: args.input.name }),
            ...(args.input.description && { description: args.input.description }),
            ...(args.input.slug && { slug: args.input.slug }),
            ...(args.input.sku !== undefined && { sku: args.input.sku }),
            ...(args.input.brand !== undefined && { brand: args.input.brand }),
            ...(args.input.tags !== undefined && { tags: args.input.tags }),
            ...(args.input.isActive !== undefined && { isActive: args.input.isActive }),
            ...(args.input.isFeatured !== undefined && { isFeatured: args.input.isFeatured }),
            ...(args.input.weight !== undefined && { weight: args.input.weight }),
            ...(args.input.dimensions !== undefined && { dimensions: args.input.dimensions }),
            ...(args.input.material !== undefined && { material: args.input.material }),
            ...(args.input.careInstructions !== undefined && { careInstructions: args.input.careInstructions }),
            ...(args.input.categoryId && { categoryId: args.input.categoryId }),
          },
          include: {
            category: true,
            variants: true,
            images: true,
          }
        })
      } catch (error) {
        throw new Error(`Failed to update product: ${error.message}`)
      }
    },
  })
)

builder.mutationField('deleteProduct', (t) =>
  t.field({
    type: 'Boolean',
    args: {
      id: t.arg.string({ required: true }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if product has orders
        const orderItemCount = await prisma.orderItem.count({
          where: {
            productVariant: {
              productId: args.id
            }
          }
        })
        
        if (orderItemCount > 0) {
          throw new Error('Cannot delete product that has been ordered')
        }
        
        // Delete related data in transaction
        await prisma.$transaction(async (tx) => {
          // Delete product variants
          await tx.productVariant.deleteMany({
            where: { productId: args.id }
          })
          
          // Delete product images
          await tx.productImage.deleteMany({
            where: { productId: args.id }
          })
          
          // Delete product reviews
          await tx.productReview.deleteMany({
            where: { productId: args.id }
          })
          
          // Delete wishlist items
          await tx.wishlistItem.deleteMany({
            where: { productId: args.id }
          })
          
          // Finally delete the product
          await tx.product.delete({
            where: { id: args.id }
          })
        })
        
        return true
      } catch (error) {
        throw new Error(`Failed to delete product: ${error.message}`)
      }
    },
  })
)

// Customer Mutations
builder.mutationField('createCustomer', (t) =>
  t.field({
    type: Customer,
    args: {
      email: t.arg.string({ required: true }),
      firstName: t.arg.string({ required: true }),
      lastName: t.arg.string({ required: true }),
      phone: t.arg.string({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        // Check if email already exists
        const existingCustomer = await prisma.customer.findUnique({
          where: { email: args.email }
        })

        if (existingCustomer) {
          throw new Error('Customer with this email already exists')
        }

        return await prisma.customer.create({
          data: {
            email: args.email,
            firstName: args.firstName,
            lastName: args.lastName,
            phone: args.phone,
          },
        })
      } catch (error) {
        throw new Error(`Failed to create customer: ${error.message}`)
      }
    },
  })
)

// Size and Color Mutations
builder.mutationField('createSize', (t) =>
  t.field({
    type: Size,
    args: {
      sizeLabel: t.arg.string({ required: true }),
      category: t.arg.string({ required: false }),
      sortOrder: t.arg.int({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        const existingSize = await prisma.size.findUnique({
          where: { sizeLabel: args.sizeLabel }
        })

        if (existingSize) {
          throw new Error('Size with this label already exists')
        }

        return await prisma.size.create({
          data: {
            sizeLabel: args.sizeLabel,
            category: args.category,
            sortOrder: args.sortOrder ?? 0,
          },
        })
      } catch (error) {
        throw new Error(`Failed to create size: ${error.message}`)
      }
    },
  })
)

builder.mutationField('createColor', (t) =>
  t.field({
    type: Color,
    args: {
      colorName: t.arg.string({ required: true }),
      hexCode: t.arg.string({ required: false }),
      sortOrder: t.arg.int({ required: false }),
    },
    resolve: async (parent, args, { prisma }) => {
      try {
        const existingColor = await prisma.color.findUnique({
          where: { colorName: args.colorName }
        })

        if (existingColor) {
          throw new Error('Color with this name already exists')
        }

        return await prisma.color.create({
          data: {
            colorName: args.colorName,
            hexCode: args.hexCode,
            sortOrder: args.sortOrder ?? 0,
          },
        })
      } catch (error) {
        throw new Error(`Failed to create color: ${error.message}`)
      }
    },
  })
)
