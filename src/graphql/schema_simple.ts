import { builder } from '../lib/builder';

// Simple test query
builder.queryField('hello', (t) =>
  t.string({
    resolve: () => 'Hello, GraphQL!',
  })
);

// Health check
builder.queryField('health', (t) =>
  t.field({
    type: builder.objectType('HealthStatus', {
      fields: (t) => ({
        status: t.string(),
        timestamp: t.string(),
      }),
    }),
    resolve: () => ({
      status: 'healthy',
      timestamp: new Date().toISOString(),
    }),
  })
);

export const schema = builder.toSchema();
