import { builder } from '../lib/builder';

// Import all type definitions to register them with the builder
import './types/pagination';
import './types/customer';
import './types/product';
import './types/order';
import './types/review';
import './types/promocode-wishlist';
import './types/utilities';
import './types/mutations';

// Build and export the schema
export const schema = builder.toSchema();

// Export types for external use
export * from './types/pagination';
