import { builder } from '../lib/builder'

// Import all types and resolvers
import '../lib/filters' // Import filter types
import './types/common' // Import enums and basic types
import './types/category'
import './types/product'
import './resolvers/queries'
import './resolvers/mutations'

// Build and export the schema
export const schema = builder.toSchema()

// Export schema as SDL for introspection
export const schemaSDL = builder.toSchema({
  sortSchema: true,
})