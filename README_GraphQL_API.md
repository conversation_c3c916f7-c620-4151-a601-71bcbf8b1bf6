# AllInCloth GraphQL API Setup Guide

A comprehensive GraphQL API built with Prisma, Pothos, and MongoDB for an e-commerce clothing store.

## Tech Stack

- **GraphQL Schema Builder**: Pothos
- **ORM**: Prisma
- **Database**: MongoDB
- **GraphQL Server**: GraphQL Yoga
- **Runtime**: Next.js API Routes
- **Type Safety**: TypeScript throughout

## Project Structure

```
src/
├── lib/
│   ├── prisma.ts          # Prisma client configuration
│   ├── builder.ts         # Pothos schema builder setup
│   ├── errors.ts          # Custom error handling
│   ├── validation.ts      # Input validation utilities
│   └── yoga.ts           # GraphQL Yoga server configuration
├── graphql/
│   ├── schema.ts         # Main GraphQL schema
│   └── types/           # GraphQL type definitions
│       ├── category.ts
│       ├── product.ts
│       ├── productImage.ts
│       ├── productVariant.ts
│       ├── size.ts
│       ├── color.ts
│       ├── discount.ts
│       ├── customer.ts
│       └── order.ts
└── app/api/
    ├── graphql/route.ts  # GraphQL endpoint
    └── health/route.ts   # Health check endpoint
```

## Features

### 🎯 Core Features
- **Type-safe GraphQL API** with Pothos
- **Prisma ORM** with MongoDB integration
- **Relay-style pagination** with cursor-based navigation
- **Comprehensive error handling** with custom error types
- **Input validation** and sanitization
- **Transaction support** for complex operations

### 🛍️ E-commerce Features
- **Product catalog** with categories, variants, and images
- **Inventory management** with stock tracking
- **Order processing** with status updates
- **Customer management** with order history
- **Discount system** with percentage and amount discounts
- **Size and color variants** with unique SKUs

### 🔍 Advanced Querying
- **Filtering and sorting** on all major entities
- **Search capabilities** across products
- **Pagination** with total count
- **Nested relations** with efficient loading
- **Virtual fields** for computed data

## Setup Instructions

### 1. Environment Configuration

Create a `.env` file:

```env
# Database
DATABASE_URL="mongodb+srv://username:<EMAIL>/allincloth?retryWrites=true&w=majority"

# Environment
NODE_ENV="development"

# Optional: Frontend URL for CORS
FRONTEND_URL="https://yourdomain.com"
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Generate Prisma Client

```bash
npx prisma generate
```

### 4. Start Development Server

```bash
npm run dev
```

### 5. Access GraphQL Playground

Visit `http://localhost:3000/api/graphql` to access GraphiQL (in development mode)

## API Architecture

### Schema Builder Configuration

The Pothos schema builder is configured with:
- **Prisma Plugin**: Direct database integration
- **Relay Plugin**: Cursor-based pagination
- **Validation Plugin**: Input validation
- **Custom Scalars**: DateTime handling

### Error Handling Strategy

- **Structured Error Types**: AppError, ValidationError, NotFoundError, etc.
- **Prisma Error Translation**: Automatic conversion of Prisma errors
- **GraphQL Error Formatting**: Consistent error response format
- **Development vs Production**: Different error detail levels

### Data Access Patterns

- **Repository Pattern**: Encapsulated in Pothos resolvers
- **Transaction Support**: Complex operations wrapped in transactions
- **Optimistic Locking**: Stock management with quantity checks
- **Relation Loading**: Efficient nested queries with Prisma

## Key API Endpoints

### GraphQL API
- **Endpoint**: `/api/graphql`
- **Methods**: GET (queries), POST (mutations)
- **Content-Type**: `application/json`

### Health Check
- **Endpoint**: `/api/health`
- **Method**: GET
- **Response**: Service and database status

## Data Model Overview

### Core Entities

1. **Category** - Product categorization
2. **Product** - Base product information
3. **ProductImage** - Product images with primary flag
4. **Size** - Available sizes (S, M, L, XL, etc.)
5. **Color** - Available colors with hex codes
6. **ProductVariant** - Specific product combinations (product + size + color)
7. **Discount** - Promotional discounts
8. **Customer** - Customer information
9. **Order** - Customer orders
10. **OrderItem** - Individual items in orders

### Relationships

- Products belong to Categories
- Products have multiple Images and Variants
- Variants combine Product + Size + Color
- Discounts can apply to multiple Variants
- Customers have multiple Orders
- Orders contain multiple OrderItems
- OrderItems reference ProductVariants

## Performance Considerations

### Database Optimization
- **Indexed Fields**: All ID fields, email, SKU
- **Compound Indexes**: Product variant uniqueness
- **Query Optimization**: Selective field loading

### GraphQL Optimization
- **N+1 Prevention**: Prisma's built-in query optimization
- **Pagination**: Cursor-based with configurable limits
- **Field Selection**: Only requested fields are loaded

### Caching Strategy
- **Prisma Query Caching**: Built-in query result caching
- **Connection Pooling**: Efficient database connections
- **Static Data**: Categories and sizes could be cached

## Security Features

### Input Validation
- **Email Validation**: RFC-compliant email checking
- **Phone Validation**: International phone number support
- **SKU Validation**: Alphanumeric with specific patterns
- **Price Validation**: Range and format checking

### Data Sanitization
- **String Trimming**: Automatic whitespace removal
- **HTML Escaping**: Prevention of XSS attacks
- **SQL Injection Prevention**: Prisma's prepared statements

### Rate Limiting (Recommended)
```typescript
// Add to yoga.ts context
import rateLimit from 'express-rate-limit'

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
})
```

## Testing Strategy

### Unit Tests
```bash
# Install testing dependencies
npm install -D jest @types/jest ts-jest

# Run tests
npm test
```

### Integration Tests
```bash
# Test GraphQL endpoints
npm run test:integration
```

### Sample Test Query
```typescript
import { createTestClient } from '@apollo/client/testing'

const GET_PRODUCTS = gql`
  query GetProducts {
    products {
      edges {
        node {
          id
          name
        }
      }
    }
  }
`

test('should fetch products', async () => {
  const { query } = createTestClient({ schema })
  const { data } = await query({ query: GET_PRODUCTS })
  expect(data.products.edges).toBeDefined()
})
```

## Deployment

### Production Environment Variables
```env
NODE_ENV="production"
DATABASE_URL="your_production_mongodb_url"
FRONTEND_URL="https://your-domain.com"
```

### Build and Deploy
```bash
# Build the application
npm run build

# Start production server
npm start
```

### Monitoring
- Health check endpoint for monitoring
- Error tracking with structured logging
- Performance metrics through GraphQL Yoga

## Extension Points

### Adding Authentication
```typescript
// In yoga.ts context function
context: async ({ request }) => {
  const token = request.headers.get('authorization')
  const user = await validateToken(token)
  return { user, request }
}
```

### Adding File Upload
```typescript
// Install graphql-upload
npm install graphql-upload @types/graphql-upload

// Add to schema
const Upload = builder.scalarType('Upload', {
  serialize: (value) => value,
  parseValue: (value) => value,
})
```

### Adding Real-time Features
```typescript
// Install graphql-subscriptions
npm install graphql-subscriptions

// Add subscriptions to schema
builder.subscriptionType({
  fields: (t) => ({
    orderUpdated: t.field({
      type: OrderType,
      subscribe: () => pubSub.asyncIterator('ORDER_UPDATED'),
      resolve: (payload) => payload,
    }),
  }),
})
```

## Troubleshooting

### Common Issues

1. **MongoDB Connection**: Ensure DATABASE_URL is correctly formatted
2. **Prisma Generation**: Run `npx prisma generate` after schema changes
3. **Type Errors**: Ensure all GraphQL types match Prisma models
4. **CORS Issues**: Configure cors settings in yoga.ts

### Debug Mode
```typescript
// Enable debug logging
const yoga = createYoga({
  logging: {
    debug: (...args) => args.forEach(arg => console.log(arg)),
    info: (...args) => args.forEach(arg => console.log(arg)),
    warn: (...args) => args.forEach(arg => console.warn(arg)),
    error: (...args) => args.forEach(arg => console.error(arg)),
  },
})
```

## Support

For issues and questions:
1. Check the GraphQL schema documentation
2. Review the example queries in `GraphQL_API_Examples.md`
3. Check server logs for detailed error information
4. Use GraphiQL for interactive query testing

---

This API provides a solid foundation for an e-commerce platform with room for extension and customization based on specific business requirements.
