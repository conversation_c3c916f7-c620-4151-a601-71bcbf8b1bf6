# AllInCloth GraphQL API

A comprehensive, robust GraphQL API built with **Prisma**, **Pothos**, and **MongoDB** that provides advanced pagination strategies and flexible querying capabilities for an e-commerce platform.

## 🚀 Features

### **Dual Pagination Strategies**
- **Cursor-based pagination** (Relay-style) with `pageInfo`
- **Traditional pagination** with `page`, `pageSize`, `offset`, and `skip`

### **Comprehensive Filtering**
- Advanced filtering options for every entity
- String filters (contains, startsWith, endsWith, equals, in, notIn)
- Date range filtering
- Number range filtering
- Boolean filtering
- Relationship-based filtering

### **Type Safety**
- Full TypeScript support with Pothos
- Zod validation for inputs
- Auto-generated Prisma types

### **Performance Optimized**
- Efficient database queries
- Connection pooling
- Optimized cursor pagination
- Field-level query optimization

## 📁 Project Structure

```
src/
├── graphql/
│   ├── schema.ts              # Main schema export
│   └── types/
│       ├── pagination.ts      # Pagination utilities and types
│       ├── customer.ts        # Customer schema and resolvers
│       ├── product.ts         # Product schema and resolvers
│       ├── order.ts           # Order schema and resolvers
│       ├── review.ts          # Review schema and resolvers
│       ├── promocode-wishlist.ts # PromoCode and Wishlist schemas
│       ├── utilities.ts       # Global search, analytics, health check
│       └── mutations.ts       # Mutation resolvers
├── lib/
│   ├── prisma.ts             # Prisma client configuration
│   └── builder.ts            # Pothos schema builder setup
└── app/api/graphql/
    └── route.ts              # GraphQL endpoint (Next.js App Router)
```

## 🔧 Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Variables
```env
DATABASE_URL="mongodb://..."
NODE_ENV="development"
```

### 3. Generate Prisma Client
```bash
npx prisma generate
```

### 4. Start Development Server
```bash
npm run dev
```

The GraphQL endpoint will be available at: `http://localhost:3000/api/graphql`

## 📚 API Documentation

### **Query Examples**

#### **1. Customers with Cursor-based Pagination**
```graphql
query GetCustomers($first: Int, $after: String, $filter: CustomerFilterInput) {
  customers(first: $first, after: $after, filter: $filter) {
    edges {
      node {
        id
        email
        firstName
        lastName
        fullName
        createdAt
      }
      cursor
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
      startCursor
      endCursor
      totalCount
    }
  }
}
```

Variables:
```json
{
  "first": 10,
  "filter": {
    "search": "john",
    "hasOrders": true,
    "createdAt": {
      "from": "2024-01-01T00:00:00Z",
      "to": "2024-12-31T23:59:59Z"
    }
  }
}
```

#### **2. Customers with Traditional Pagination**
```graphql
query GetCustomersList($pagination: PaginationInput, $filter: CustomerFilterInput) {
  customersList(pagination: $pagination, filter: $filter) {
    items {
      id
      email
      firstName
      lastName
      fullName
      createdAt
    }
    pagination {
      currentPage
      pageSize
      totalItems
      totalPages
      hasNextPage
      hasPreviousPage
    }
  }
}
```

Variables:
```json
{
  "pagination": {
    "page": 1,
    "pageSize": 20
  },
  "filter": {
    "email": {
      "contains": "@gmail.com"
    },
    "hasWishlist": true
  }
}
```

#### **3. Products with Advanced Filtering**
```graphql
query GetProducts($filter: ProductFilterInput, $sort: [SortInput!]) {
  products(first: 20, filter: $filter, sort: $sort) {
    edges {
      node {
        id
        name
        description
        averageRating
        reviewCount
        minPrice
        maxPrice
        primaryImage {
          imageUrl
        }
        category {
          name
        }
        variants {
          id
          price
          quantity
          isInStock
          discountedPrice
          size {
            sizeLabel
          }
          color {
            colorName
            hexCode
          }
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
      totalCount
    }
  }
}
```

Variables:
```json
{
  "filter": {
    "categoryIds": ["cat-1", "cat-2"],
    "priceRange": {
      "min": 10,
      "max": 100
    },
    "inStock": true,
    "ratingMin": 4,
    "tags": ["summer", "casual"],
    "search": "shirt"
  },
  "sort": [
    {
      "field": "rating",
      "direction": "DESC"
    },
    {
      "field": "price",
      "direction": "ASC"
    }
  ]
}
```

#### **4. Orders with Complex Filtering**
```graphql
query GetOrders($filter: OrderFilterInput) {
  ordersList(
    pagination: { page: 1, pageSize: 10 }
    filter: $filter
    sort: [{ field: "ORDER_DATE", direction: "DESC" }]
  ) {
    items {
      id
      orderDate
      totalAmount
      status
      itemCount
      totalItems
      customer {
        firstName
        lastName
        email
      }
      items {
        quantity
        priceAtPurchase
        totalPrice
        productVariant {
          sku
          product {
            name
          }
          size {
            sizeLabel
          }
          color {
            colorName
          }
        }
      }
      paymentTransaction {
        status
        transactionId
        amount
      }
    }
    pagination {
      totalItems
      totalPages
      hasNextPage
    }
  }
}
```

#### **5. Global Search**
```graphql
query GlobalSearch($input: GlobalSearchInput!) {
  globalSearch(input: $input) {
    products {
      id
      name
      description
    }
    customers {
      id
      firstName
      lastName
      email
    }
    orders {
      id
      orderDate
      totalAmount
      status
    }
    totalResults
  }
}
```

Variables:
```json
{
  "input": {
    "query": "john smith",
    "types": ["customer", "order"],
    "limit": 5
  }
}
```

#### **6. Sales Analytics**
```graphql
query SalesAnalytics($dateRange: AnalyticsDateRangeInput) {
  salesAnalytics(dateRange: $dateRange) {
    totalRevenue
    totalOrders
    averageOrderValue
    topSellingProducts {
      product {
        name
      }
      totalSold
      revenue
    }
    salesByMonth {
      month
      year
      totalOrders
      revenue
    }
  }
}
```

### **Mutation Examples**

#### **1. Create Customer**
```graphql
mutation CreateCustomer($input: CreateCustomerInput!) {
  createCustomer(input: $input) {
    success
    message
    customer {
      id
      email
      firstName
      lastName
    }
  }
}
```

Variables:
```json
{
  "input": {
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567890"
  }
}
```

## 🎛️ Advanced Features

### **Filter Capabilities**

#### **String Filtering**
```graphql
{
  customersList(filter: {
    firstName: {
      contains: "john"        # Case-insensitive contains
      startsWith: "Jo"        # Starts with
      endsWith: "hn"          # Ends with
      equals: "John"          # Exact match
      in: ["John", "Jane"]    # In list
      notIn: ["Admin"]        # Not in list
    }
  }) {
    items { firstName }
  }
}
```

#### **Date Range Filtering**
```graphql
{
  ordersList(filter: {
    orderDate: {
      from: "2024-01-01T00:00:00Z"
      to: "2024-12-31T23:59:59Z"
    }
  }) {
    items { orderDate }
  }
}
```

#### **Number Range Filtering**
```graphql
{
  productsList(filter: {
    priceRange: {
      min: 10.00
      max: 100.00
    }
  }) {
    items { name }
  }
}
```

#### **Relationship Filtering**
```graphql
{
  customersList(filter: {
    hasOrders: true           # Has any orders
    hasWishlist: false        # No wishlist
    hasReviews: true          # Has written reviews
  }) {
    items { email }
  }
}
```

### **Sorting Options**

All list queries support flexible sorting:

```graphql
{
  productsList(sort: [
    { field: "rating", direction: "DESC" }
    { field: "price", direction: "ASC" }
    { field: "createdAt", direction: "DESC" }
  ]) {
    items { name }
  }
}
```

### **Pagination Strategies**

#### **Cursor-based (Relay)**
- Use for infinite scroll
- Better for large datasets
- Consistent results during data changes

```graphql
{
  products(first: 10, after: "cursor123") {
    edges { node { name } cursor }
    pageInfo { hasNextPage endCursor totalCount }
  }
}
```

#### **Traditional (Offset)**
- Use for numbered pagination
- Easier to implement page numbers
- Better for small to medium datasets

```graphql
{
  productsList(pagination: { page: 2, pageSize: 20 }) {
    items { name }
    pagination { currentPage totalPages hasNextPage }
  }
}
```

## 🔍 Performance Tips

1. **Use field selection** - Only request fields you need
2. **Leverage cursor pagination** for large datasets
3. **Combine filters** to reduce dataset size
4. **Use connection pooling** in production
5. **Monitor query complexity** with GraphQL depth limiting

## 🛠️ Extension Points

The API is designed to be easily extensible:

1. **Add new entities** by creating new schema files
2. **Extend filtering** by adding new filter input types
3. **Add computed fields** using field resolvers
4. **Implement subscriptions** for real-time updates
5. **Add authorization** using Pothos auth plugin

## 📊 GraphQL Playground

In development mode, access GraphQL Playground at:
`http://localhost:3000/api/graphql`

This provides:
- Interactive query building
- Schema exploration
- Query validation
- Response formatting

## 🚀 Production Considerations

1. **Environment Variables**: Set `NODE_ENV=production`
2. **Database Connection**: Use connection pooling
3. **Rate Limiting**: Implement query complexity analysis
4. **Caching**: Add Redis for query caching
5. **Monitoring**: Set up performance monitoring
6. **Security**: Implement authentication and authorization

## 📝 Schema Overview

The API provides comprehensive coverage of an e-commerce platform:

- **Customers**: User management and profiles
- **Products**: Catalog management with variants
- **Orders**: Order processing and tracking
- **Reviews**: Customer feedback system
- **Wishlist**: Save for later functionality
- **PromoCode**: Discount and promotion system
- **Analytics**: Business intelligence queries
- **Search**: Global search capabilities

Every entity supports both pagination strategies and comprehensive filtering, making this API suitable for any client application requirements.
