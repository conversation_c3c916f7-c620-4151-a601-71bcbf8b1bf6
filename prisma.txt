generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model Category {
  id    String   @id @default(auto()) @map("_id")
  name  String   @unique
  products Product[]
}

model Product {
  id          String           @id @default(auto()) @map("_id")
  name        String
  description String
  category    Category         @relation(fields: [categoryId], references: [id])
  categoryId  String
  variants    ProductVariant[]
  images      ProductImage[]
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
}

model ProductImage {
  id        String   @id @default(auto()) @map("_id")
  product   Product  @relation(fields: [productId], references: [id])
  productId String
  imageUrl  String
  isPrimary Boolean  @default(false)
}

model Size {
  id         String           @id @default(auto()) @map("_id")
  sizeLabel  String           @unique
  variants   ProductVariant[]
}

model Color {
  id         String           @id @default(auto()) @map("_id")
  colorName  String           @unique
  hexCode    String?
  variants   ProductVariant[]
}

model ProductVariant {
  id         String   @id @default(auto()) @map("_id")
  product    Product  @relation(fields: [productId], references: [id])
  productId  String

  size       Size     @relation(fields: [sizeId], references: [id])
  sizeId     String

  color      Color    @relation(fields: [colorId], references: [id])
  colorId    String

  sku        String   @unique
  price      Decimal
  quantity   Int

  discounts  VariantDiscount[]

  @@unique([productId, sizeId, colorId])
}

model Discount {
  id              String            @id @default(auto()) @map("_id")
  name            String
  description     String?
  discountPercent Decimal?
  discountAmount  Decimal?
  startDate       DateTime
  endDate         DateTime
  variants        VariantDiscount[]
}

model VariantDiscount {
  id                String         @id @default(auto()) @map("_id")
  productVariant    ProductVariant @relation(fields: [productVariantId], references: [id])
  productVariantId  String

  discount          Discount       @relation(fields: [discountId], references: [id])
  discountId        String

  @@unique([productVariantId, discountId])
}

model Customer {
  id         String    @id @default(auto()) @map("_id")
  email      String    @unique
  firstName  String
  lastName   String
  phone      String?
  createdAt  DateTime  @default(now())
  orders     Order[]
}

model Order {
  id          String       @id @default(auto()) @map("_id")
  customer    Customer     @relation(fields: [customerId], references: [id])
  customerId  String
  orderDate   DateTime     @default(now())
  totalAmount Decimal
  status      OrderStatus  @default(pending)
  items       OrderItem[]
}

model OrderItem {
  id                String         @id @default(auto()) @map("_id")
  order             Order          @relation(fields: [orderId], references: [id])
  orderId           String

  productVariant    ProductVariant @relation(fields: [productVariantId], references: [id])
  productVariantId  String

  quantity          Int
  priceAtPurchase   Decimal
}

enum OrderStatus {
  pending
  shipped
  delivered
  cancelled
}
