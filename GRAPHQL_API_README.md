# AllInCloth - E-commerce GraphQL API

A comprehensive GraphQL API for an e-commerce clothing platform built with modern technologies including Prisma ORM, Pothos GraphQL schema builder, and MongoDB.

## 🚀 Features

- **Type-safe GraphQL API** with Pothos schema builder
- **Prisma ORM** with MongoDB integration
- **Dual Pagination Support** - Both cursor-based and traditional (page/pageSize/offset/skip)
- **Comprehensive Filtering** - Advanced filtering options for all queries
- **Full CRUD Operations** - Complete Create, Read, Update, Delete functionality
- **Error Handling** - Robust error handling with custom error types
- **Input Validation** - Comprehensive input validation and sanitization
- **Performance Optimized** - Efficient database queries and caching strategies

## 📋 Table of Contents

- [Database Schema](#database-schema)
- [API Documentation](#api-documentation)
- [Setup Instructions](#setup-instructions)
- [Usage Examples](#usage-examples)
- [Pagination](#pagination)
- [Filtering](#filtering)
- [Error Handling](#error-handling)

## 🗄️ Database Schema

### Core Entities

#### Categories
Hierarchical category system supporting parent-child relationships.

**Fields:**
- `id` - Unique identifier
- `name` - Category name (unique)
- `description` - Optional description
- `slug` - URL-friendly identifier (unique)
- `isActive` - Active status
- `sortOrder` - Display order
- `parentId` - Parent category reference
- `createdAt/updatedAt` - Timestamps

**Purpose:** Organize products into hierarchical categories (e.g., Clothing > Men's > Shirts)

#### Products
Main product information with rich metadata.

**Fields:**
- `id` - Unique identifier
- `name` - Product name
- `description` - Product description
- `slug` - URL-friendly identifier (unique)
- `sku` - Stock Keeping Unit (optional, unique)
- `brand` - Product brand
- `tags` - Array of tags for search/filtering
- `isActive` - Active status
- `isFeatured` - Featured product flag
- `weight` - Product weight
- `dimensions` - JSON object with dimensions
- `material` - Product material
- `careInstructions` - Care instructions
- `categoryId` - Category reference
- `createdAt/updatedAt` - Timestamps

**Purpose:** Store core product information and metadata for the e-commerce catalog

#### ProductVariants
Specific variations of products (size/color combinations).

**Fields:**
- `id` - Unique identifier
- `productId` - Product reference
- `sizeId` - Size reference
- `colorId` - Color reference
- `sku` - Unique SKU for this variant
- `price` - Current price
- `compareAtPrice` - Original price (for discounts)
- `costPrice` - Cost price (for profit calculation)
- `quantity` - Available stock
- `lowStockThreshold` - Low stock alert threshold
- `isActive` - Active status
- `weight` - Variant-specific weight
- `barcode` - Barcode (optional, unique)
- `createdAt/updatedAt` - Timestamps

**Purpose:** Handle inventory and pricing for specific product variations

#### Customers
Customer account information and preferences.

**Fields:**
- `id` - Unique identifier
- `email` - Email address (unique)
- `firstName/lastName` - Customer name
- `phone` - Phone number (optional)
- `dateOfBirth` - Date of birth (optional)
- `gender` - Gender preference (enum)
- `isActive` - Account status
- `isVerified` - Email verification status
- `lastLoginAt` - Last login timestamp
- `createdAt/updatedAt` - Timestamps

**Purpose:** Manage customer accounts and personal information

#### Orders
Order information and status tracking.

**Fields:**
- `id` - Unique identifier
- `orderNumber` - Human-readable order number (unique)
- `customerId` - Customer reference
- `orderDate` - Order creation date
- `subtotalAmount` - Subtotal before tax/shipping
- `taxAmount` - Tax amount
- `shippingAmount` - Shipping cost
- `discountAmount` - Total discount applied
- `totalAmount` - Final total amount
- `status` - Order status (enum)
- `paymentStatus` - Payment status (enum)
- `shippingMethod` - Shipping method
- `trackingNumber` - Shipping tracking number
- `notes` - Order notes
- `shippingAddress` - JSON snapshot of shipping address
- `billingAddress` - JSON snapshot of billing address
- `discountId` - Applied discount reference
- `createdAt/updatedAt` - Timestamps

**Purpose:** Track customer orders from creation to fulfillment

### Supporting Entities

#### Sizes
Available product sizes.

**Fields:**
- `id` - Unique identifier
- `sizeLabel` - Size label (e.g., "S", "M", "L", "XL")
- `category` - Size category (e.g., "clothing", "shoes")
- `sortOrder` - Display order
- `isActive` - Active status
- `createdAt` - Creation timestamp

**Purpose:** Standardize size options across products

#### Colors
Available product colors.

**Fields:**
- `id` - Unique identifier
- `colorName` - Color name (unique)
- `hexCode` - Hex color code (optional)
- `isActive` - Active status
- `sortOrder` - Display order
- `createdAt` - Creation timestamp

**Purpose:** Standardize color options across products

#### ProductImages
Product image management.

**Fields:**
- `id` - Unique identifier
- `productId` - Product reference
- `imageUrl` - Image URL
- `altText` - Alt text for accessibility
- `isPrimary` - Primary image flag
- `sortOrder` - Display order
- `createdAt` - Creation timestamp

**Purpose:** Manage product images with proper ordering and accessibility

#### ProductReviews
Customer product reviews and ratings.

**Fields:**
- `id` - Unique identifier
- `productId` - Product reference
- `customerId` - Customer reference
- `rating` - Rating (1-5 stars)
- `title` - Review title (optional)
- `comment` - Review comment (optional)
- `isVerified` - Verified purchase flag
- `isApproved` - Moderation approval status
- `createdAt/updatedAt` - Timestamps

**Purpose:** Enable customer feedback and social proof

#### Addresses
Customer shipping and billing addresses.

**Fields:**
- `id` - Unique identifier
- `customerId` - Customer reference
- `type` - Address type (SHIPPING/BILLING)
- `firstName/lastName` - Recipient name
- `company` - Company name (optional)
- `address1/address2` - Address lines
- `city/state/postalCode/country` - Location details
- `phone` - Contact phone (optional)
- `isDefault` - Default address flag
- `createdAt/updatedAt` - Timestamps

**Purpose:** Store customer shipping and billing addresses

#### Cart & CartItems
Shopping cart functionality.

**Cart Fields:**
- `id` - Unique identifier
- `customerId` - Customer reference (unique)
- `createdAt/updatedAt` - Timestamps

**CartItem Fields:**
- `id` - Unique identifier
- `cartId` - Cart reference
- `productVariantId` - Product variant reference
- `quantity` - Item quantity
- `addedAt` - Addition timestamp

**Purpose:** Persistent shopping cart for logged-in customers

#### Wishlist & WishlistItems
Product wishlist functionality.

**Wishlist Fields:**
- `id` - Unique identifier
- `customerId` - Customer reference (unique)
- `createdAt/updatedAt` - Timestamps

**WishlistItem Fields:**
- `id` - Unique identifier
- `wishlistId` - Wishlist reference
- `productId` - Product reference
- `addedAt` - Addition timestamp

**Purpose:** Allow customers to save products for later

#### Discounts & VariantDiscounts
Discount and promotion system.

**Discount Fields:**
- `id` - Unique identifier
- `name` - Discount name
- `description` - Discount description
- `code` - Coupon code (optional, unique)
- `discountType` - Type (PERCENTAGE/FIXED_AMOUNT)
- `discountPercent` - Percentage discount
- `discountAmount` - Fixed amount discount
- `minimumAmount` - Minimum order amount
- `maximumDiscount` - Maximum discount amount
- `usageLimit` - Total usage limit
- `usageCount` - Current usage count
- `isActive` - Active status
- `startDate/endDate` - Validity period
- `createdAt/updatedAt` - Timestamps

**VariantDiscount Fields:**
- `id` - Unique identifier
- `productVariantId` - Product variant reference
- `discountId` - Discount reference

**Purpose:** Flexible discount system supporting various promotion types

## 🛠️ Setup Instructions

### Prerequisites

- Node.js 18+
- MongoDB database (local or cloud)
- npm or yarn package manager

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd allincloth
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Configuration**
Create a `.env` file in the root directory:
```env
# Database
DATABASE_URL="mongodb://localhost:27017/allincloth"
# or for MongoDB Atlas:
# DATABASE_URL="mongodb+srv://username:<EMAIL>/allincloth"

# Environment
NODE_ENV="development"

# GraphQL
GRAPHQL_ENDPOINT="/api/graphql"

# CORS (for production)
ALLOWED_ORIGINS="https://yourdomain.com,https://admin.yourdomain.com"
```

4. **Database Setup**
```bash
# Generate Prisma client
npx prisma generate

# Push schema to database (for development)
npx prisma db push

# Or run migrations (for production)
npx prisma migrate deploy
```

5. **Start the development server**
```bash
npm run dev
```

6. **Access GraphQL Playground**
Open [http://localhost:3000/api/graphql](http://localhost:3000/api/graphql) in your browser

## 📚 API Documentation

### GraphQL Endpoint
- **URL:** `/api/graphql`
- **Methods:** GET, POST, OPTIONS
- **Content-Type:** `application/json`

### Authentication
Currently, the API is set up without authentication. In a production environment, you would add:
- JWT token validation
- Role-based access control
- Rate limiting
- API key authentication

### Available Queries

#### Categories
```graphql
# Get all categories with pagination
query GetCategories($pagination: PaginationInput, $search: String) {
  categories(pagination: $pagination, search: $search) {
    id
    name
    description
    slug
    isActive
    sortOrder
    productCount
    isParent
    parent {
      id
      name
    }
    children {
      id
      name
    }
  }
}

# Get single category
query GetCategory($id: String, $slug: String) {
  category(id: $id, slug: $slug) {
    id
    name
    description
    slug
    products {
      id
      name
      slug
      minPrice
      totalStock
    }
  }
}

# Cursor-based pagination
query GetCategoriesConnection($pagination: CursorPaginationInput) {
  categoriesConnection(pagination: $pagination) {
    edges {
      node {
        id
        name
        slug
      }
      cursor
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
      startCursor
      endCursor
    }
    totalCount
  }
}
```

#### Products
```graphql
# Get all products with filtering and pagination
query GetProducts(
  $where: ProductWhereInput
  $orderBy: [ProductOrderByInput!]
  $pagination: PaginationInput
  $search: String
) {
  products(
    where: $where
    orderBy: $orderBy
    pagination: $pagination
    search: $search
  ) {
    id
    name
    description
    slug
    brand
    tags
    isActive
    isFeatured
    minPrice
    totalStock
    averageRating
    category {
      id
      name
    }
    variants {
      id
      sku
      price
      quantity
      size {
        sizeLabel
      }
      color {
        colorName
        hexCode
      }
    }
    images {
      id
      imageUrl
      altText
      isPrimary
    }
  }
}

# Get single product
query GetProduct($id: String, $slug: String) {
  product(id: $id, slug: $slug) {
    id
    name
    description
    slug
    brand
    tags
    material
    careInstructions
    category {
      id
      name
      slug
    }
    variants {
      id
      sku
      price
      compareAtPrice
      quantity
      isLowStock
      isInStock
      discountedPrice
      size {
        id
        sizeLabel
      }
      color {
        id
        colorName
        hexCode
      }
    }
    images {
      id
      imageUrl
      altText
      isPrimary
      sortOrder
    }
    reviews {
      id
      rating
      title
      comment
      isVerified
      customer {
        firstName
        lastName
      }
      createdAt
    }
  }
}
```

### Available Mutations

#### Category Mutations
```graphql
# Create category
mutation CreateCategory($input: CategoryCreateInput!) {
  createCategory(input: $input) {
    id
    name
    slug
    description
    isActive
    sortOrder
    parentId
  }
}

# Update category
mutation UpdateCategory($id: String!, $input: CategoryUpdateInput!) {
  updateCategory(id: $id, input: $input) {
    id
    name
    slug
    description
    isActive
    sortOrder
  }
}

# Delete category
mutation DeleteCategory($id: String!) {
  deleteCategory(id: $id)
}
```

#### Product Mutations
```graphql
# Create product
mutation CreateProduct($input: ProductCreateInput!) {
  createProduct(input: $input) {
    id
    name
    slug
    description
    brand
    categoryId
    isActive
    isFeatured
  }
}

# Update product
mutation UpdateProduct($id: String!, $input: ProductUpdateInput!) {
  updateProduct(id: $id, input: $input) {
    id
    name
    slug
    description
    brand
    isActive
    isFeatured
  }
}

# Delete product
mutation DeleteProduct($id: String!) {
  deleteProduct(id: $id)
}
```

#### Customer Mutations
```graphql
# Create customer
mutation CreateCustomer(
  $email: String!
  $firstName: String!
  $lastName: String!
  $phone: String
) {
  createCustomer(
    email: $email
    firstName: $firstName
    lastName: $lastName
    phone: $phone
  ) {
    id
    email
    firstName
    lastName
    phone
    isActive
    isVerified
  }
}
```

## 📄 Pagination

The API supports both cursor-based and traditional pagination methods.

### Traditional Pagination
Use `PaginationInput` for offset-based pagination:

```graphql
input PaginationInput {
  page: Int          # Page number (1-based)
  pageSize: Int      # Number of items per page
  offset: Int        # Number of items to skip
  skip: Int          # Alias for offset
}
```

**Example:**
```graphql
query GetProducts($pagination: PaginationInput) {
  products(pagination: { page: 1, pageSize: 20 }) {
    id
    name
    price
  }
}
```

### Cursor-based Pagination
Use `CursorPaginationInput` for cursor-based pagination:

```graphql
input CursorPaginationInput {
  first: Int         # Number of items to fetch from start
  after: String      # Cursor to start after
  last: Int          # Number of items to fetch from end
  before: String     # Cursor to end before
}
```

**Example:**
```graphql
query GetProductsConnection($pagination: CursorPaginationInput) {
  productsConnection(pagination: { first: 20, after: "cursor123" }) {
    edges {
      node {
        id
        name
      }
      cursor
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
      startCursor
      endCursor
    }
    totalCount
  }
}
```

## 🔍 Filtering

The API provides comprehensive filtering options for all queries.

### String Filters
```graphql
input StringFilter {
  equals: String
  not: String
  in: [String!]
  notIn: [String!]
  contains: String
  startsWith: String
  endsWith: String
  mode: QueryMode    # DEFAULT or INSENSITIVE
}
```

### Number Filters
```graphql
input IntFilter {
  equals: Int
  not: Int
  in: [Int!]
  notIn: [Int!]
  lt: Int           # Less than
  lte: Int          # Less than or equal
  gt: Int           # Greater than
  gte: Int          # Greater than or equal
}

input FloatFilter {
  equals: Float
  not: Float
  in: [Float!]
  notIn: [Float!]
  lt: Float
  lte: Float
  gt: Float
  gte: Float
}
```

### Boolean Filters
```graphql
input BooleanFilter {
  equals: Boolean
  not: Boolean
}
```

### DateTime Filters
```graphql
input DateTimeFilter {
  equals: DateTime
  not: DateTime
  in: [DateTime!]
  notIn: [DateTime!]
  lt: DateTime
  lte: DateTime
  gt: DateTime
  gte: DateTime
}
```

### Example Filtering
```graphql
query GetFilteredProducts {
  products(
    where: {
      name: { contains: "shirt", mode: INSENSITIVE }
      isActive: { equals: true }
      isFeatured: { equals: true }
      brand: { in: ["Nike", "Adidas", "Puma"] }
      minPrice: 20.0
      maxPrice: 100.0
      createdAt: { gte: "2024-01-01T00:00:00Z" }
    }
    orderBy: [{ isFeatured: DESC }, { createdAt: DESC }]
  ) {
    id
    name
    brand
    minPrice
  }
}
```

## ⚠️ Error Handling

The API implements comprehensive error handling with custom error types.

### Error Types

#### ValidationError
Thrown when input validation fails:
```json
{
  "errors": [
    {
      "message": "Invalid email format",
      "extensions": {
        "code": "VALIDATION_ERROR",
        "field": "email"
      }
    }
  ]
}
```

#### NotFoundError
Thrown when a requested resource doesn't exist:
```json
{
  "errors": [
    {
      "message": "Product with identifier 'invalid-id' not found",
      "extensions": {
        "code": "NOT_FOUND"
      }
    }
  ]
}
```

#### ConflictError
Thrown when there's a conflict (e.g., duplicate unique fields):
```json
{
  "errors": [
    {
      "message": "Product with this slug already exists",
      "extensions": {
        "code": "CONFLICT"
      }
    }
  ]
}
```

### Input Validation

The API validates all inputs according to business rules:

- **Email**: Must be valid email format
- **Slug**: Lowercase letters, numbers, and hyphens only
- **SKU**: Uppercase letters, numbers, hyphens, and underscores
- **Price**: Must be positive number
- **Quantity**: Must be non-negative integer
- **Rating**: Must be integer between 1 and 5

## 💡 Usage Examples

### Example Variables for Queries

#### Creating a Category
```json
{
  "input": {
    "name": "Men's Clothing",
    "description": "Clothing items for men",
    "slug": "mens-clothing",
    "isActive": true,
    "sortOrder": 1
  }
}
```

#### Creating a Product
```json
{
  "input": {
    "name": "Classic Cotton T-Shirt",
    "description": "Comfortable cotton t-shirt perfect for everyday wear",
    "slug": "classic-cotton-tshirt",
    "sku": "CCT-001",
    "brand": "ComfortWear",
    "tags": ["cotton", "casual", "comfortable"],
    "isActive": true,
    "isFeatured": false,
    "weight": 0.2,
    "material": "100% Cotton",
    "careInstructions": "Machine wash cold, tumble dry low",
    "categoryId": "category-id-here"
  }
}
```

#### Filtering Products
```json
{
  "where": {
    "isActive": { "equals": true },
    "brand": { "in": ["Nike", "Adidas"] },
    "name": { "contains": "shirt", "mode": "INSENSITIVE" }
  },
  "orderBy": [
    { "isFeatured": "DESC" },
    { "createdAt": "DESC" }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20
  }
}
```

#### Search with Pagination
```json
{
  "search": "cotton shirt",
  "pagination": {
    "page": 1,
    "pageSize": 12
  },
  "orderBy": [
    { "isFeatured": "DESC" },
    { "name": "ASC" }
  ]
}
```

## 🚀 Performance Considerations

### Database Optimization
- **Indexes**: Proper indexes on frequently queried fields
- **Aggregations**: Efficient aggregation queries for computed fields
- **Batch Loading**: DataLoader pattern for N+1 query prevention
- **Connection Pooling**: Prisma connection pooling

### Caching Strategy
- **Query Caching**: Cache frequently accessed data
- **CDN**: Use CDN for static assets (images)
- **Redis**: Implement Redis for session and cache storage

### Rate Limiting
Implement rate limiting in production:
```typescript
// Example rate limiting middleware
const rateLimit = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
}
```

## 🔒 Security Best Practices

### Authentication & Authorization
```typescript
// Example context with authentication
context: async ({ request }) => {
  const token = request.headers.get('authorization')?.replace('Bearer ', '')
  const user = await validateToken(token)

  return {
    prisma,
    user,
    isAuthenticated: !!user,
  }
}
```

### Input Sanitization
- All string inputs are trimmed and validated
- SQL injection prevention through Prisma ORM
- XSS prevention through proper output encoding
- File upload validation for images

### CORS Configuration
```typescript
cors: {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || [],
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
}
```

## 📊 Monitoring & Logging

### GraphQL Metrics
- Query complexity analysis
- Execution time monitoring
- Error rate tracking
- Popular query patterns

### Database Monitoring
- Query performance metrics
- Connection pool status
- Slow query identification
- Index usage analysis

## 🧪 Testing

### Unit Tests
```bash
npm run test
```

### Integration Tests
```bash
npm run test:integration
```

### GraphQL Schema Testing
```bash
npm run test:schema
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review existing issues and discussions

---

**Built with ❤️ using modern GraphQL technologies**
